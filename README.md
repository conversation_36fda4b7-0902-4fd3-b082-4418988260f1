# Rylie AI - Automotive Sales Assistant

## New Features Implementation

The following features have been implemented:

### 1. Advanced Handover Logic & Lead Management

- **Customizable triggers for human escalation** - Configure triggers based on sentiment, urgency, repeated questions, and keywords
- **Lead scoring system** - Score leads based on engagement, urgency, and buyer signals
- **Automated follow-up scheduling** - Schedule and manage follow-ups with reminders for sales staff

### 2. Dealership Branding & Persona Customization

- **Self-serve branding in admin panel** - Upload logo, set colors, pick persona preset, customize welcome messages
- **Persona preview mode** - Preview persona changes before publishing live
- **Advanced persona controls** - Tune tone and vocabulary per channel

### 3. Seamless Onboarding & User Management

- **User flows** - Smooth invite process, role assignment, and guided onboarding
- **User role management & audit logs** - Enable batch user operations, permission adjustments, and track admin actions

### 4. Extended Customer Insights

- **Customer journey & history** - Map buyer journeys, track interaction history, and create persistent profiles
- **Preference visualization** - Track and visualize customer preferences and predicted buying windows

## Setup Instructions

1. Run the database setup script to create the new tables:

```bash
npm run setup-new-features
```

2. Restart the server to initialize the new features:

```bash
npm run start
```

## Feature Usage

### Customizable Escalation Triggers

Access the escalation triggers through the admin panel under Settings > Handover Configuration > Triggers. You can create custom triggers based on:

- Sentiment analysis
- Urgency detection
- Repeated questions
- Specific keywords
- Custom logic combinations

### Lead Scoring

The system automatically scores leads based on:

- Urgency signals (keywords like "today", "asap", "soon")
- Engagement level (message count and length)
- Buyer signals (mentions of price, financing, test drive)
- Specific questions asked

### Follow-up Scheduling

Schedule follow-ups from the conversation view:

1. Click "Schedule Follow-up" in any conversation
2. Set the date, time, and assign to a team member
3. Add notes for context
4. Receive reminders via email when the follow-up is due

### Persona Preview

When customizing your dealership's persona:

1. Go to Admin > Branding & Persona
2. Make your changes to the persona settings
3. Click "Show Persona Preview" to see how it will appear to customers
4. Save your changes when satisfied

### User Management

Invite new users through the admin panel:

1. Go to Admin > Users > Invite User
2. Enter email address and select role
3. The user will receive an email invitation to join
4. All user actions are tracked in the audit logs

### Customer Insights

View detailed customer insights:

1. Go to Customers > Customer Profiles
2. Select a customer to view their journey
3. See preferences, interaction history, and predicted buying window
4. Use this information to personalize follow-ups

## API Endpoints

The following new API endpoints are available:

### Escalation Triggers
- `GET /api/dealerships/:dealershipId/escalation-triggers` - Get all triggers
- `POST /api/dealerships/:dealershipId/escalation-triggers` - Create a trigger
- `PUT /api/dealerships/:dealershipId/escalation-triggers/:triggerId` - Update a trigger
- `DELETE /api/dealerships/:dealershipId/escalation-triggers/:triggerId` - Delete a trigger

### Lead Management
- `GET /api/conversations/:conversationId/lead-score` - Get lead score
- `GET /api/dealerships/:dealershipId/top-leads` - Get top leads
- `POST /api/follow-ups` - Schedule a follow-up
- `GET /api/my-follow-ups` - Get current user's follow-ups
- `PUT /api/follow-ups/:followUpId/complete` - Complete a follow-up
- `PUT /api/follow-ups/:followUpId/cancel` - Cancel a follow-up

### User Management
- `POST /api/dealerships/:dealershipId/invitations` - Create invitation
- `POST /api/invitations/accept` - Accept invitation
- `GET /api/dealerships/:dealershipId/invitations` - Get pending invitations
- `DELETE /api/dealerships/:dealershipId/invitations/:invitationId` - Cancel invitation
- `GET /api/dealerships/:dealershipId/audit-logs` - Get audit logs

### Customer Insights
- `POST /api/dealerships/:dealershipId/customer-profiles` - Create profile
- `POST /api/customer-profiles/:profileId/interactions` - Record interaction
- `GET /api/customer-profiles/:profileId/journey` - Get customer journey
- `GET /api/customer-profiles/:profileId/preferences` - Analyze preferences
- `GET /api/customer-profiles/:profileId/buying-window` - Predict buying window