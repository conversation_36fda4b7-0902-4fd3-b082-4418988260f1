import React, { ReactNode, useEffect } from "react";
import { useLocation, useRoute } from "wouter";
import { useAuth } from "@/hooks/useAuth";
import { Loader2 } from "lucide-react";

interface ProtectedRouteProps {
  children: ReactNode;
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const [, setLocation] = useLocation();
  const [isAuthPage] = useRoute("/auth");
  const [isLoginPage] = useRoute("/login");
  
  // Handle route protection logic
  useEffect(() => {
    if (!isLoading && !isAuthenticated && !isAuthPage && !isLoginPage) {
      // Save the current path to redirect back after login
      localStorage.setItem("redirectAfterLogin", window.location.pathname);
      setLocation("/login");
    }
  }, [isAuthenticated, isLoading, isAuthPage, isLoginPage, setLocation]);
  
  // Show loading indicator while authentication state is being determined
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Loading...</span>
      </div>
    );
  }
  
  // If not authenticated, return null (redirect will happen via useEffect)
  if (!isAuthenticated && !isAuthPage && !isLoginPage) {
    return null;
  }
  
  // Otherwise, render the children
  return <>{children}</>;
}