import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Bo<PERSON>, User } from 'lucide-react';

interface PersonaPreviewProps {
  persona: {
    name: string;
    tone: string;
    template?: string;
    welcomeMessage?: string;
  };
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  logoUrl?: string;
}

export const PersonaPreview: React.FC<PersonaPreviewProps> = ({
  persona,
  primaryColor,
  secondaryColor,
  accentColor,
  logoUrl
}) => {
  // Generate sample messages based on persona tone
  const getSampleMessages = () => {
    const toneMap = {
      friendly: [
        "Hi there! I'm so glad you reached out. How can I help you today?",
        "That's a great question about our inventory! We have several options that might work for you."
      ],
      professional: [
        "Welcome. I'd be pleased to assist you with your automotive needs today.",
        "Regarding your inquiry about financing options, we offer several competitive programs."
      ],
      casual: [
        "Hey! Thanks for dropping by. What can I help you with?",
        "Cool question! Yeah, we've got a few models that match what you're looking for."
      ],
      formal: [
        "Good day. Thank you for contacting us. How may I be of assistance?",
        "In response to your inquiry, I would like to provide the following information."
      ]
    };
    
    return toneMap[persona.tone] || toneMap.friendly;
  };
  
  const sampleMessages = getSampleMessages();
  
  return (
    <Card className="shadow-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span>Persona Preview</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="border rounded-lg p-4">
          {/* Chat header */}
          <div 
            className="pb-3 mb-3 border-b flex items-center justify-between"
            style={{ borderColor: secondaryColor }}
          >
            <div className="flex items-center gap-2">
              {logoUrl ? (
                <img 
                  src={logoUrl} 
                  alt="Dealership Logo" 
                  className="h-8 max-w-[100px] object-contain"
                />
              ) : (
                <div 
                  className="h-8 w-8 rounded-full flex items-center justify-center text-white"
                  style={{ backgroundColor: primaryColor }}
                >
                  <Bot className="h-4 w-4" />
                </div>
              )}
              <div>
                <div className="font-medium">
                  {persona.name}
                </div>
                <div className="text-xs text-muted-foreground">
                  Online
                </div>
              </div>
            </div>
          </div>
          
          {/* Welcome message */}
          <div className="space-y-4">
            <div className="flex gap-2">
              <div 
                className="h-8 w-8 rounded-full flex-shrink-0 flex items-center justify-center text-white"
                style={{ backgroundColor: primaryColor }}
              >
                <Bot className="h-4 w-4" />
              </div>
              <div 
                className="p-3 rounded-lg rounded-tl-none max-w-[80%]"
                style={{ 
                  backgroundColor: secondaryColor,
                  color: primaryColor
                }}
              >
                {persona.welcomeMessage || 
                  `Hello! I'm ${persona.name}, your personal assistant. How can I help you today?`}
              </div>
            </div>
            
            {/* Sample customer message */}
            <div className="flex gap-2 justify-end">
              <div 
                className="p-3 rounded-lg rounded-tr-none max-w-[80%]"
                style={{ 
                  backgroundColor: accentColor, 
                  color: 'white'
                }}
              >
                I'm interested in learning about your SUV options.
              </div>
              <div className="h-8 w-8 rounded-full flex-shrink-0 flex items-center justify-center text-white bg-gray-500">
                <User className="h-4 w-4" />
              </div>
            </div>
            
            {/* Sample response based on tone */}
            <div className="flex gap-2">
              <div 
                className="h-8 w-8 rounded-full flex-shrink-0 flex items-center justify-center text-white"
                style={{ backgroundColor: primaryColor }}
              >
                <Bot className="h-4 w-4" />
              </div>
              <div 
                className="p-3 rounded-lg rounded-tl-none max-w-[80%]"
                style={{ 
                  backgroundColor: secondaryColor,
                  color: primaryColor
                }}
              >
                {sampleMessages[0]}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};