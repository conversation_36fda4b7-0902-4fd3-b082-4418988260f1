// A very simple script to test our system prompt without using the complex interface
import 'dotenv/config';
import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

const DEFAULT_SYSTEM_PROMPT = `System Prompt for Automotive Sales Agent AI
You are an automotive sales agent named {{agentName}}, working for {{dealershipName}}, a dealership committed to exceptional customer service and top-quality vehicles. Your job is to connect with customers, understand their needs, and guide them toward purchasing a vehicle from our inventory that perfectly fits their lifestyle. You're a real person to them—never hint that you're an AI or break character. Your focus is on building trust, sparking excitement, and making the car-buying process smooth and enjoyable.

Dealership Contact
Contact Details: Visit our dealership or contact us online

Resource URLs:
- Financing: {{financingUrl}}
- Trade-In Valuation: {{tradeInUrl}}
- Appointment Scheduling: {{appointmentUrl}}
- Browse Inventory: {{inventoryUrl}}

Specific Instructions
{{specificInstructions}}

Specific Constraints
{{specificConstraints}}

Always be friendly, helpful, and knowledgeable about vehicles. When customers ask about financing, direct them to {{financingUrl}}. For trade-ins, use {{tradeInUrl}}. To schedule appointments, use {{appointmentUrl}}.`;

async function testPrompt() {
  const customizedPrompt = DEFAULT_SYSTEM_PROMPT
    .replace('{{agentName}}', 'Rylie')
    .replace('{{dealershipName}}', 'Example Motors')
    .replace('{{financingUrl}}', 'https://www.exampledealership.com/financing')
    .replace('{{tradeInUrl}}', 'https://www.exampledealership.com/trade-in-value')
    .replace('{{appointmentUrl}}', 'https://www.exampledealership.com/schedule-appointment')
    .replace('{{inventoryUrl}}', 'https://www.exampledealership.com/inventory')
    .replace('{{specificInstructions}}', 'Always start with a casual, personal greeting. Use contractions and everyday words.')
    .replace('{{specificConstraints}}', 'No Pricing or Promises: Avoid discussing costs, financing details, or delivery guarantees.');

  // Test the prompt
  const customerMessage = "Hi there, I'm looking for a new SUV for my family. Can you help me?";
  
  console.log(`Testing system prompt with customer message: "${customerMessage}"`);
  console.log('\n--- Customized System Prompt ---\n');
  console.log(customizedPrompt);
  console.log('\n--- Starting OpenAI Request ---\n');
  
  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: customizedPrompt
        },
        {
          role: 'user',
          content: customerMessage
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    });
    
    const response = completion.choices[0].message.content;
    console.log('\n--- AI Response ---\n');
    console.log(response);
  } catch (error) {
    console.error('Error testing prompt:', error);
  }
}

// Run the test
testPrompt();