import winston from 'winston';
import 'winston-daily-rotate-file';
import path from 'path';

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');

// Configure logger with custom format
const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json()
  ),
  defaultMeta: { service: 'auth-service' },
  transports: [
    // Console transport
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.printf(
          ({ timestamp, level, message, ...meta }) => 
            `${timestamp} ${level}: ${message} ${
              Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''
            }`
        )
      )
    }),
  ]
});

// In production, add file-based logging
if (process.env.NODE_ENV === 'production') {
  logger.add(
    new winston.transports.DailyRotateFile({
      filename: path.join(logsDir, 'application-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d',
      level: 'info'
    })
  );

  logger.add(
    new winston.transports.DailyRotateFile({
      filename: path.join(logsDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d',
      level: 'error'
    })
  );
}

// Helper functions to log with context
export default {
  info: (message: string, context?: any) => {
    logger.info(message, context);
  },

  warn: (message: string, context?: any) => {
    logger.warn(message, context);
  },

  error: (message: string, error?: Error | null, context?: any) => {
    if (error) {
      logger.error(`${message}: ${error.message}`, {
        ...context,
        stack: error.stack
      });
    } else {
      logger.error(message, context);
    }
  },

  debug: (message: string, context?: any) => {
    logger.debug(message, context);
  }
};