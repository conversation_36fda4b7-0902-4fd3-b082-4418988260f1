// Simple in-memory cache implementation for authentication
// This is a basic implementation that can be enhanced later

interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: string;
}

class MemoryCache {
  private cache: Map<string, any>;
  private hits: number;
  private misses: number;

  constructor() {
    this.cache = new Map();
    this.hits = 0;
    this.misses = 0;
  }

  get(key: string): any {
    if (this.cache.has(key)) {
      this.hits++;
      return this.cache.get(key);
    }
    this.misses++;
    return null;
  }

  set(key: string, value: any, ttl: number = 3600000): void {
    this.cache.set(key, value);
    
    // Auto-expire after TTL
    if (ttl > 0) {
      setTimeout(() => {
        this.delete(key);
      }, ttl);
    }
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.hits = 0;
    this.misses = 0;
  }

  getStats(): CacheStats {
    const total = this.hits + this.misses;
    const hitRate = total > 0 ? (this.hits / total) * 100 : 0;
    
    return {
      size: this.cache.size,
      hits: this.hits,
      misses: this.misses,
      hitRate: `${hitRate.toFixed(2)}%`
    };
  }
}

// Create a singleton instance
const memoryCache = new MemoryCache();

export const getCacheStats = (): CacheStats => {
  return memoryCache.getStats();
};

export const shutdownCache = async (): Promise<void> => {
  memoryCache.clear();
  console.log('Cache cleared successfully');
  return Promise.resolve();
};

export default memoryCache;