import { Router } from 'express';
import { z } from 'zod';
import { sendInvitation, verifyToken, activateAccount } from '../services/magic-link-auth';

// Create router for public magic link routes (no authentication required)
const router = Router();

// Schema for magic link request
const requestMagicLinkSchema = z.object({
  email: z.string().email('Invalid email address'),
});

// Schema for magic link verification
const verifyMagicLinkSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  email: z.string().email('Invalid email address'),
});

/**
 * Route for sending a magic link invitation
 * POST /api/public/magic-link/send
 */
router.post('/send', async (req, res) => {
  try {
    // Validate request body
    const validationResult = requestMagicLinkSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: validationResult.error.issues[0].message,
      });
    }
    
    const { email } = validationResult.data;
    
    // Get base URL for magic link
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    
    // Send magic link invitation
    const result = await sendInvitation(email, baseUrl);
    
    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: result.error,
      });
    }
    
    // Return success response (don't include the token in the response)
    return res.status(200).json({
      success: true,
      message: 'Magic link has been sent to your email',
    });
  } catch (error) {
    console.error('Error sending magic link:', error);
    return res.status(500).json({
      success: false,
      error: 'An error occurred while sending the magic link',
    });
  }
});

/**
 * Route for verifying a magic link token
 * POST /api/public/magic-link/verify
 */
router.post('/verify', async (req, res) => {
  try {
    // Validate request body
    const validationResult = verifyMagicLinkSchema.safeParse(req.body);
    
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: validationResult.error.issues[0].message,
      });
    }
    
    const { token, email } = validationResult.data;
    
    // Verify the token and activate the account
    const activationResult = await activateAccount(token, email);
    
    if (!activationResult.success) {
      return res.status(400).json({
        success: false,
        error: activationResult.error,
      });
    }
    
    // Log the user in
    req.login(activationResult.user, (err) => {
      if (err) {
        console.error('Error logging in user after magic link verification:', err);
        return res.status(500).json({
          success: false,
          error: 'Failed to log in after verification',
        });
      }
      
      // Return success response
      return res.status(200).json({
        success: true,
        message: 'Magic link verified successfully',
      });
    });
  } catch (error) {
    console.error('Error verifying magic link:', error);
    return res.status(500).json({
      success: false,
      error: 'An error occurred while verifying the magic link',
    });
  }
});

export default router;