import { Router } from 'express';
import { db } from '../db';
import { auditLogs, securityEvents, apiKeyAccessLogs, users, dealerships } from '../../shared/schema';
import { searchAuditLogs, generateComplianceReport, logAuditEvent } from '../services/audit-logger';
import { z } from 'zod';
import { and, desc, eq, gte, ilike, lte, or } from 'drizzle-orm';
import { log } from '../vite';
import logger from '../utils/logger';

const router = Router();

// Note: Authentication middleware (isAuthenticated, isAdmin) is applied at the route registration

// Schema for validating audit log query parameters
const auditLogQuerySchema = z.object({
  page: z.coerce.number().positive().default(1),
  pageSize: z.coerce.number().positive().max(100).default(20),
  eventType: z.string().optional(),
  category: z.string().optional(),
  riskLevel: z.enum(['high', 'medium', 'low']).optional(),
  dealershipId: z.coerce.number().optional(),
  userId: z.coerce.number().optional(),
  search: z.string().optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
});

// GET /api/audit-logs - Fetch audit logs with filtering
router.get('/', async (req: any, res: any) => {
  const { 
    page, 
    pageSize, 
    eventType, 
    category, 
    riskLevel, 
    dealershipId, 
    userId, 
    search,
    fromDate,
    toDate
  } = auditLogQuerySchema.parse(req.query);
  
  const offset = (page - 1) * pageSize;
  
  try {
    // Start building the query
    let query = db.select().from(auditLogs);
    let countQuery = db.select({ count: db.fn.count() }).from(auditLogs);
    
    // Apply filters
    const filters = [];
    
    if (eventType) {
      filters.push(eq(auditLogs.eventType, eventType as any));
    }
    
    if (dealershipId) {
      filters.push(eq(auditLogs.dealershipId, dealershipId));
    }
    
    if (userId) {
      filters.push(eq(auditLogs.userId, userId));
    }
    
    // Date range filtering
    if (fromDate && toDate) {
      const fromDateObj = new Date(fromDate);
      const toDateObj = new Date(toDate);
      filters.push(and(
        gte(auditLogs.createdAt, fromDateObj),
        lte(auditLogs.createdAt, toDateObj)
      ));
    } else if (fromDate) {
      filters.push(gte(auditLogs.createdAt, new Date(fromDate)));
    } else if (toDate) {
      filters.push(lte(auditLogs.createdAt, new Date(toDate)));
    }
    
    // Search in IP address, resource ID, or details
    if (search) {
      filters.push(or(
        ilike(auditLogs.ipAddress || '', `%${search}%`),
        ilike(auditLogs.resourceId || '', `%${search}%`),
      ));
      // Note: JSON/JSONB search would be implemented with a more specific DB function for production
    }
    
    // Apply all filters if any exist
    if (filters.length > 0) {
      const combinedFilter = filters.length === 1 ? filters[0] : and(...filters);
      query = query.where(combinedFilter);
      countQuery = countQuery.where(combinedFilter);
    }
    
    // Apply post-query filtering for JSON fields (meta.riskLevel and meta.eventCategory)
    const logs = await query.orderBy(desc(auditLogs.createdAt)).limit(pageSize).offset(offset);
    
    let filteredLogs = logs;
    
    // Filter by risk level if specified (this is a JSON field so we filter in JS)
    if (riskLevel) {
      filteredLogs = filteredLogs.filter((log: any) => 
        log.details?.meta?.riskLevel === riskLevel
      );
    }
    
    // Filter by category if specified (this is a JSON field so we filter in JS)
    if (category) {
      filteredLogs = filteredLogs.filter((log: any) => 
        log.details?.meta?.eventCategory === category
      );
    }
    
    // Get total count for pagination
    const [countResult] = await countQuery;
    const total = Number(countResult?.count || 0);
    
    res.json({
      logs: filteredLogs,
      page,
      pageSize,
      total
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    res.status(500).json({ error: 'Failed to fetch audit logs' });
  }
});

// Schema for validating security events query parameters
const securityEventsQuerySchema = z.object({
  page: z.coerce.number().positive().default(1),
  pageSize: z.coerce.number().positive().max(100).default(20),
  dealershipId: z.coerce.number().optional(),
  severity: z.enum(['info', 'warning', 'critical']).optional(),
  search: z.string().optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
});

// GET /api/security-events - Fetch security events with filtering
router.get('/security-events', async (req: any, res: any) => {
  const { 
    page, 
    pageSize, 
    dealershipId, 
    severity, 
    search,
    fromDate,
    toDate
  } = securityEventsQuerySchema.parse(req.query);
  
  const offset = (page - 1) * pageSize;
  
  try {
    // Start building the query
    let query = db.select().from(securityEvents);
    let countQuery = db.select({ count: db.fn.count() }).from(securityEvents);
    
    // Apply filters
    const filters = [];
    
    if (dealershipId) {
      filters.push(eq(securityEvents.dealershipId, dealershipId));
    }
    
    if (severity) {
      filters.push(eq(securityEvents.severity, severity));
    }
    
    // Date range filtering
    if (fromDate && toDate) {
      const fromDateObj = new Date(fromDate);
      const toDateObj = new Date(toDate);
      filters.push(and(
        gte(securityEvents.timestamp, fromDateObj),
        lte(securityEvents.timestamp, toDateObj)
      ));
    } else if (fromDate) {
      filters.push(gte(securityEvents.timestamp, new Date(fromDate)));
    } else if (toDate) {
      filters.push(lte(securityEvents.timestamp, new Date(toDate)));
    }
    
    // Search in IP address or path
    if (search) {
      filters.push(or(
        ilike(securityEvents.ip, `%${search}%`),
        ilike(securityEvents.path || '', `%${search}%`),
        ilike(securityEvents.type, `%${search}%`)
      ));
    }
    
    // Apply all filters if any exist
    if (filters.length > 0) {
      const combinedFilter = filters.length === 1 ? filters[0] : and(...filters);
      query = query.where(combinedFilter);
      countQuery = countQuery.where(combinedFilter);
    }
    
    // Execute the query
    const events = await query.orderBy(desc(securityEvents.timestamp)).limit(pageSize).offset(offset);
    
    // Get total count for pagination
    const [countResult] = await countQuery;
    const total = Number(countResult?.count || 0);
    
    res.json({
      events,
      page,
      pageSize,
      total
    });
  } catch (error) {
    console.error('Error fetching security events:', error);
    res.status(500).json({ error: 'Failed to fetch security events' });
  }
});

// Schema for validating API access logs query parameters
const apiAccessLogsQuerySchema = z.object({
  page: z.coerce.number().positive().default(1),
  pageSize: z.coerce.number().positive().max(100).default(20),
  apiKeyId: z.coerce.number().optional(),
  dealershipId: z.coerce.number().optional(),
  search: z.string().optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
});

// GET /api/api-access-logs - Fetch API access logs with filtering
router.get('/api-access-logs', async (req: any, res: any) => {
  const { 
    page, 
    pageSize, 
    apiKeyId, 
    dealershipId, 
    search,
    fromDate,
    toDate
  } = apiAccessLogsQuerySchema.parse(req.query);
  
  const offset = (page - 1) * pageSize;
  
  try {
    // Start building the query
    let query = db.select().from(apiKeyAccessLogs);
    let countQuery = db.select({ count: db.fn.count() }).from(apiKeyAccessLogs);
    
    // Apply filters
    const filters = [];
    
    if (apiKeyId) {
      filters.push(eq(apiKeyAccessLogs.apiKeyId, apiKeyId));
    }
    
    // Date range filtering
    if (fromDate && toDate) {
      const fromDateObj = new Date(fromDate);
      const toDateObj = new Date(toDate);
      filters.push(and(
        gte(apiKeyAccessLogs.timestamp, fromDateObj),
        lte(apiKeyAccessLogs.timestamp, toDateObj)
      ));
    } else if (fromDate) {
      filters.push(gte(apiKeyAccessLogs.timestamp, new Date(fromDate)));
    } else if (toDate) {
      filters.push(lte(apiKeyAccessLogs.timestamp, new Date(toDate)));
    }
    
    // Search in IP address or endpoint
    if (search) {
      filters.push(or(
        ilike(apiKeyAccessLogs.ip, `%${search}%`),
        ilike(apiKeyAccessLogs.endpoint, `%${search}%`)
      ));
    }
    
    // Apply all filters if any exist
    if (filters.length > 0) {
      const combinedFilter = filters.length === 1 ? filters[0] : and(...filters);
      query = query.where(combinedFilter);
      countQuery = countQuery.where(combinedFilter);
    }
    
    // Execute the query
    const logs = await query.orderBy(desc(apiKeyAccessLogs.timestamp)).limit(pageSize).offset(offset);
    
    // Get total count for pagination
    const [countResult] = await countQuery;
    const total = Number(countResult?.count || 0);
    
    res.json({
      logs,
      page,
      pageSize,
      total
    });
  } catch (error) {
    console.error('Error fetching API access logs:', error);
    res.status(500).json({ error: 'Failed to fetch API access logs' });
  }
});

// Schema for validation compliance report generation request
const complianceReportSchema = z.object({
  dealershipId: z.coerce.number(),
  startDate: z.string(),
  endDate: z.string(),
  reportType: z.enum(['full', 'summary']).default('full'),
});

// POST /api/audit-logs/generate-report - Generate a compliance report
router.post('/generate-report', async (req: any, res: any) => {
  const { dealershipId, startDate, endDate, reportType } = complianceReportSchema.parse(req.body);
  
  try {
    const report = await generateComplianceReport(
      dealershipId, 
      new Date(startDate), 
      new Date(endDate)
    );
    
    // Log the report generation as an audit event
    const userId = req.user?.id;
    const userIp = req.ip;
    const userAgent = req.headers['user-agent'];
    
    // Log this action itself
    await logAuditEvent({
      eventType: 'data_export',
      userId: userId ? parseInt(userId as string) : undefined,
      dealershipId,
      resourceType: 'compliance_report',
      resourceId: dealershipId.toString(),
      ipAddress: userIp,
      userAgent,
      details: {
        reportType,
        timeframe: { startDate, endDate },
      }
    });
    
    // Return the appropriate report format
    if (reportType === 'summary') {
      // Return a summarized version
      const { dealership, reportPeriod, summary } = report;
      res.json({ dealership, reportPeriod, summary });
    } else {
      // Return full report
      res.json(report);
    }
  } catch (error) {
    console.error('Error generating compliance report:', error);
    res.status(500).json({ error: 'Failed to generate compliance report' });
  }
});

export default router;