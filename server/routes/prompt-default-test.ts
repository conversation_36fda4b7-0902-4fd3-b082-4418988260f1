/**
 * Express routes for testing the default system prompt
 * These routes don't require a database connection and use OpenAI directly
 */
import express from 'express';
import { DEFAULT_SYSTEM_PROMPT } from '../services/system-prompts/default';
import OpenAI from 'openai';
import logger from '../utils/logger';

const router = express.Router();

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

/**
 * POST /api/prompt-default-test
 * Test the default system prompt with a customer message
 */
router.post('/', async (req, res) => {
  try {
    const { 
      customerMessage, 
      personaArguments, 
      previousMessages = [],
      sampleInventory = '',
      systemPrompt = ''  // Accept custom system prompt
    } = req.body;

    if (!customerMessage) {
      return res.status(400).json({ 
        success: false, 
        message: 'Customer message is required' 
      });
    }

    // Format the system prompt with placeholders replaced
    let formattedPrompt = systemPrompt || DEFAULT_SYSTEM_PROMPT;
    
    // Replace placeholders in the prompt
    formattedPrompt = formattedPrompt
      .replace('[ARG-Agent Name]', personaArguments?.dealerName || 'Rylie')
      .replace('[ARG-Employer Name]', personaArguments?.dealershipName || 'PureCars Dealership')
      .replace('[ARG-Information About Employer]', personaArguments?.customInstructions || 'A family-owned dealership since 1987')
      .replace('[ARG-Products]', personaArguments?.specialties?.join(', ') || 'New and certified pre-owned vehicles')
      .replace('[ARG-Employer Contact Details]', `Phone: ${personaArguments?.phoneNumber || '************'}, Email: ${personaArguments?.salesEmail || '<EMAIL>'}`)
      .replace('[ARG-Name]', personaArguments?.dealerName || 'Alex')
      .replace('[ARG-Contact Details]', `Phone: ${personaArguments?.phoneNumber || '************'}`)
      .replace('[INPUT-Product Inventory]', sampleInventory || 'No inventory provided')
      .replace('[INPUT-CUSTOMER NAME]', 'Customer');
    
    // Add conversation history if available
    if (previousMessages.length > 0) {
      const historyText = previousMessages
        .map(msg => `${msg.role === 'customer' ? 'Customer' : 'Rylie'}: ${msg.content}`)
        .join('\n\n');
      
      formattedPrompt = formattedPrompt.replace('[INPUT-CONVERSATION]', historyText);
    } else {
      formattedPrompt = formattedPrompt.replace('[INPUT-CONVERSATION]', 'No previous conversation');
    }
    
    // Prepare messages for OpenAI
    const messages = [
      { role: 'system' as const, content: formattedPrompt },
    ];
    
    // Add conversation history if available
    if (previousMessages.length > 0) {
      previousMessages.forEach(msg => {
        messages.push({
          role: msg.role === 'customer' ? 'user' as const : 'assistant' as const,
          content: msg.content
        });
      });
    }
    
    // Add the new customer message
    messages.push({ role: 'user' as const, content: customerMessage });

    // Start timing
    const startTime = Date.now();
    
    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-4-turbo-preview',
      messages: messages,
      temperature: 0.7,
      max_tokens: 800,
      response_format: { type: 'json_object' }
    });
    
    // Calculate response time
    const responseTime = Date.now() - startTime;
    
    // Extract and parse the response
    const responseContent = completion.choices[0].message.content;
    
    if (!responseContent) {
      return res.status(500).json({ 
        success: false, 
        message: 'Empty response from OpenAI' 
      });
    }
    
    try {
      // Parse the JSON response
      const jsonResponse = JSON.parse(responseContent);
      
      // Extract and clean the answer field (if needed)
      const answerText = jsonResponse.answer || 'No answer provided';
      
      return res.json({
        success: true,
        response: answerText,
        jsonResponse,
        responseTime,
        model: completion.model,
        usage: completion.usage
      });
    } catch (error) {
      logger.error('Error parsing JSON response from OpenAI', { error, raw: responseContent });
      
      return res.status(500).json({
        success: false,
        message: 'Failed to parse OpenAI response',
        rawResponse: responseContent
      });
    }
  } catch (error) {
    logger.error('Error processing prompt test', { error });
    
    return res.status(500).json({
      success: false,
      message: error.message || 'An error occurred while processing the prompt test'
    });
  }
});

/**
 * POST /api/prompt-default-test/handover
 * Create a test handover dossier using the conversation history
 */
router.post('/handover', async (req, res) => {
  try {
    const { 
      previousMessages = [],
      personaArguments = {},
      reason = "Test handover requested" 
    } = req.body;

    if (!previousMessages || previousMessages.length === 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Conversation history is required for handover' 
      });
    }

    // Create a simplified handover dossier for testing
    const customerName = personaArguments?.customerName || 'Test Customer';
    const customerContact = personaArguments?.customerPhone || personaArguments?.customerEmail || '<EMAIL>';
    const conversationSummary = `Test handover for conversation with ${customerName}`;
    
    // Extract conversation history
    const formattedConversation = previousMessages.map(msg => ({
      role: msg.role,
      content: msg.content,
      timestamp: new Date()
    }));

    // Create a mock handover dossier
    const handoverDossier = {
      customerName,
      customerContact,
      dealershipId: 1,
      conversationId: Math.floor(Math.random() * 10000),
      conversationSummary,
      customerInsights: [
        { key: 'Budget', value: 'Around $30,000', confidence: 0.85 },
        { key: 'Timeline', value: 'Looking to purchase within 2 weeks', confidence: 0.9 }
      ],
      vehicleInterests: [
        { make: 'Honda', model: 'Accord', year: 2023, trim: 'Sport', confidence: 0.8 }
      ],
      suggestedApproach: 'Customer is ready for a test drive appointment. Follow up with specific financing options.',
      urgency: 'high',
      fullConversationHistory: formattedConversation,
      escalationReason: reason
    };

    return res.json({
      success: true,
      dossier: handoverDossier,
      message: 'Handover dossier created successfully'
    });
  } catch (error) {
    logger.error('Error creating test handover dossier', { error });
    
    return res.status(500).json({
      success: false,
      message: error.message || 'An error occurred while creating the handover dossier'
    });
  }
});

export default router;