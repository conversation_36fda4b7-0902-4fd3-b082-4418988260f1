import { Router } from 'express';
import { OpenAI } from 'openai';
import { z } from 'zod';
import logger from '../utils/logger';

const router = Router();

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Validation schema for the request body
const promptTestSchema = z.object({
  systemPrompt: z.string(),
  customerMessage: z.string()
});

router.post('/api/test-system-prompt', async (req, res) => {
  try {
    // Validate the request body
    const validatedData = promptTestSchema.parse(req.body);
    const { systemPrompt, customerMessage } = validatedData;

    logger.info('Testing system prompt', {
      messageLength: customerMessage.length
    });
    
    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: customerMessage
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    });
    
    const response = completion.choices[0].message.content;
    
    // Return the AI's response
    return res.status(200).json({
      response,
      usage: completion.usage
    });
  } catch (error) {
    logger.error('Error testing system prompt', { 
      error: (error as Error).message,
      stack: (error as Error).stack
    });
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid request data', details: error.errors });
    }
    
    return res.status(500).json({ error: 'Failed to test system prompt', message: (error as Error).message });
  }
});

export default router;