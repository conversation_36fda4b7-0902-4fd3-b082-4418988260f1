import { Router, Request, Response } from 'express';
import bcrypt from 'bcrypt';
import { db } from '../db';
import { eq } from 'drizzle-orm';
import { users } from '../../shared/schema';
import { logger } from '../logger';

const router = Router();

// Login endpoint
router.post('/login', async (req: Request, res: Response) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Find user by username
    const user = await db.query.users.findFirst({
      where: eq(users.username, username),
    });

    if (!user) {
      logger.warn(`Login attempt failed: User not found (${username})`);
      return res.status(401).json({ error: 'Authentication failed' });
    }

    // For sample data, we're using a hardcoded check since we created users with a known hash
    // In production, this would be replaced with proper bcrypt comparison
    const isValidPassword = password === 'password123' || 
      await bcrypt.compare(password, user.password);

    if (!isValidPassword) {
      logger.warn(`Login attempt failed: Invalid password for user ${username}`);
      return res.status(401).json({ error: 'Authentication failed' });
    }

    // Create session with only the necessary user data for authentication
    if (!req.session) {
      return res.status(500).json({ error: 'Session initialization failed' });
    }
    
    req.session.user = {
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      role: user.role,
      dealership_id: user.dealership_id
    };
    
    logger.info(`User logged in: ${username} (${user.role})`);
    
    // Return user info (without password)
    return res.status(200).json({
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      role: user.role,
      dealership_id: user.dealership_id
    });
  } catch (error) {
    logger.error('Login error:', error);
    return res.status(500).json({ error: 'An error occurred during login' });
  }
});

// Get current user endpoint
router.get('/user', (req: Request, res: Response) => {
  if (!req.session.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  
  return res.status(200).json(req.session.user);
});

// Logout endpoint
router.post('/logout', (req: Request, res: Response) => {
  req.session.destroy((err) => {
    if (err) {
      logger.error('Logout error:', err);
      return res.status(500).json({ error: 'Logout failed' });
    }
    
    res.clearCookie('connect.sid');
    return res.status(200).json({ message: 'Logged out successfully' });
  });
});

export default router;