import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { api<PERSON>ey<PERSON>uth } from '../middleware/auth';
import logger from '../utils/logger';

const router = Router();

// Apply API key authentication to all routes
router.use(apiKeyAuth);

// Get all prompt experiments for a dealership
router.get('/', async (req: Request, res: Response) => {
  try {
    const dealershipId = parseInt(req.query.dealershipId as string);
    
    if (isNaN(dealershipId)) {
      return res.status(400).json({ error: 'Invalid dealership ID' });
    }
    
    const onlyActive = req.query.active === 'true';
    
    const experiments = onlyActive 
      ? await storage.getActivePromptExperiments(dealershipId)
      : await storage.getPromptExperiments(dealershipId);
    
    // Fetch variant details for each experiment
    const enrichedExperiments = await Promise.all(experiments.map(async (experiment) => {
      try {
        const experimentWithVariants = await storage.getPromptExperimentWithVariants(experiment.id);
        
        // Format the response to include variant details
        const variantsWithDetails = [];
        if (experimentWithVariants && experimentWithVariants.variants) {
          for (const variant of experimentWithVariants.variants) {
            const variantDetails = await storage.getPromptVariant(variant.variantId);
            if (variantDetails) {
              variantsWithDetails.push({
                variantId: variant.variantId,
                name: variantDetails.name,
                content: variantDetails.promptTemplate,
                isControl: variant.isControl,
                trafficAllocation: variant.trafficAllocation
              });
            }
          }
        }
        
        return {
          ...experiment,
          variants: variantsWithDetails
        };
      } catch (error) {
        logger.error('Failed to enrich experiment with variants', { experimentId: experiment.id, error });
        return {
          ...experiment,
          variants: []
        };
      }
    }));
    
    return res.json(enrichedExperiments);
  } catch (error) {
    logger.error('Failed to get prompt experiments', { error });
    return res.status(500).json({ error: 'Failed to get prompt experiments' });
  }
});

// Get a single prompt experiment with its variants
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid experiment ID' });
    }
    
    const experiment = await storage.getPromptExperimentWithVariants(id);
    
    if (!experiment) {
      return res.status(404).json({ error: 'Prompt experiment not found' });
    }
    
    // Fetch variant details for each variant in the experiment
    const variantsWithDetails = [];
    if (experiment.variants) {
      for (const variant of experiment.variants) {
        const variantDetails = await storage.getPromptVariant(variant.variantId);
        if (variantDetails) {
          variantsWithDetails.push({
            variantId: variant.variantId,
            name: variantDetails.name,
            content: variantDetails.promptTemplate,
            isControl: variant.isControl,
            trafficAllocation: variant.trafficAllocation
          });
        }
      }
    }
    
    const enrichedExperiment = {
      ...experiment,
      variants: variantsWithDetails
    };
    
    return res.json(enrichedExperiment);
  } catch (error) {
    logger.error('Failed to get prompt experiment', { error });
    return res.status(500).json({ error: 'Failed to get prompt experiment' });
  }
});

// Create a new prompt experiment
router.post('/', async (req: Request, res: Response) => {
  try {
    // Define the schema for creating an experiment
    const experimentSchema = z.object({
      name: z.string().min(1),
      description: z.string().optional(),
      dealershipId: z.number(),
      isActive: z.boolean().default(true),
      startDate: z.string().optional(), // ISO date string
      endDate: z.string().optional(),   // ISO date string
      experimentVariants: z.array(z.object({
        variantId: z.number(),
        trafficAllocation: z.number().min(0).max(100),
        isControl: z.boolean().default(false)
      })).min(1)
    });
    
    const validatedData = experimentSchema.parse(req.body);
    
    // Make sure traffic allocations sum to 100%
    const totalAllocation = validatedData.experimentVariants.reduce(
      (sum, variant) => sum + variant.trafficAllocation, 
      0
    );
    
    // Allow a small margin of error due to floating point arithmetic
    if (totalAllocation < 99.9 || totalAllocation > 100.1) {
      return res.status(400).json({ 
        error: 'Invalid traffic allocation', 
        message: 'Total traffic allocation must sum to 100%' 
      });
    }
    
    // Ensure we have exactly one control variant
    const controlVariants = validatedData.experimentVariants.filter(v => v.isControl);
    if (controlVariants.length !== 1) {
      return res.status(400).json({ 
        error: 'Invalid control variant configuration', 
        message: 'Exactly one variant must be designated as the control' 
      });
    }
    
    // Create the experiment
    const experiment = await storage.createPromptExperiment({
      name: validatedData.name,
      description: validatedData.description || null,
      dealershipId: validatedData.dealershipId,
      isActive: validatedData.isActive,
      startDate: validatedData.startDate ? new Date(validatedData.startDate) : new Date(),
      endDate: validatedData.endDate ? new Date(validatedData.endDate) : null
    });
    
    // Add variants to the experiment
    for (const variant of validatedData.experimentVariants) {
      await storage.addVariantToExperiment(
        experiment.id,
        variant.variantId,
        variant.trafficAllocation,
        variant.isControl
      );
    }
    
    // Get the fully populated experiment with variants
    const createdExperiment = await storage.getPromptExperimentWithVariants(experiment.id);
    
    return res.status(201).json(createdExperiment);
  } catch (error) {
    logger.error('Failed to create prompt experiment', { error });
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid experiment data', details: error.errors });
    }
    
    return res.status(500).json({ error: 'Failed to create prompt experiment' });
  }
});

// Update an existing prompt experiment
router.patch('/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid experiment ID' });
    }
    
    // Define the schema for updating an experiment
    const experimentSchema = z.object({
      name: z.string().min(1).optional(),
      description: z.string().nullable().optional(),
      isActive: z.boolean().optional(),
      startDate: z.string().optional(), // ISO date string
      endDate: z.string().nullable().optional() // ISO date string, can be null to remove end date
    });
    
    const validatedData = experimentSchema.parse(req.body);
    
    // Convert date strings to Date objects
    const updateData: any = { ...validatedData };
    if (validatedData.startDate) {
      updateData.startDate = new Date(validatedData.startDate);
    }
    if (validatedData.endDate !== undefined) {
      updateData.endDate = validatedData.endDate ? new Date(validatedData.endDate) : null;
    }
    
    // Update the experiment
    const updatedExperiment = await storage.updatePromptExperiment(id, updateData);
    
    if (!updatedExperiment) {
      return res.status(404).json({ error: 'Prompt experiment not found' });
    }
    
    // Get the fully populated experiment with variants
    const fullExperiment = await storage.getPromptExperimentWithVariants(id);
    
    return res.json(fullExperiment);
  } catch (error) {
    logger.error('Failed to update prompt experiment', { error });
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid experiment data', details: error.errors });
    }
    
    return res.status(500).json({ error: 'Failed to update prompt experiment' });
  }
});

// Add a variant to an experiment
router.post('/:id/variants', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid experiment ID' });
    }
    
    // Define the schema for adding a variant
    const variantSchema = z.object({
      variantId: z.number(),
      trafficAllocation: z.number().min(0).max(100),
      isControl: z.boolean().default(false)
    });
    
    const validatedData = variantSchema.parse(req.body);
    
    // Check if we need to reallocate traffic
    if (validatedData.isControl) {
      // If this is a new control variant, we need to update the existing control
      const experiment = await storage.getPromptExperimentWithVariants(id);
      
      if (!experiment) {
        return res.status(404).json({ error: 'Prompt experiment not found' });
      }
      
      // Update existing control variant(s)
      if (experiment.variants) {
        for (const variant of experiment.variants) {
          if (variant.isControl) {
            await storage.updateVariantTrafficAllocation(
              id,
              variant.variantId,
              variant.trafficAllocation,
              false // not control anymore
            );
          }
        }
      }
    }
    
    // Add the variant to the experiment
    const experimentVariant = await storage.addVariantToExperiment(
      id,
      validatedData.variantId,
      validatedData.trafficAllocation,
      validatedData.isControl
    );
    
    return res.status(201).json(experimentVariant);
  } catch (error) {
    logger.error('Failed to add variant to experiment', { error });
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid variant data', details: error.errors });
    }
    
    return res.status(500).json({ error: 'Failed to add variant to experiment' });
  }
});

// Update a variant in an experiment
router.patch('/:id/variants/:variantId', async (req: Request, res: Response) => {
  try {
    const experimentId = parseInt(req.params.id);
    const variantId = parseInt(req.params.variantId);
    
    if (isNaN(experimentId) || isNaN(variantId)) {
      return res.status(400).json({ error: 'Invalid ID' });
    }
    
    // Define the schema for updating a variant
    const variantSchema = z.object({
      trafficAllocation: z.number().min(0).max(100),
      isControl: z.boolean().optional()
    });
    
    const validatedData = variantSchema.parse(req.body);
    
    // Check if we need to update control status
    if (validatedData.isControl === true) {
      // If this is a new control variant, we need to update the existing control
      const experiment = await storage.getPromptExperimentWithVariants(experimentId);
      
      if (!experiment) {
        return res.status(404).json({ error: 'Prompt experiment not found' });
      }
      
      // Update existing control variant(s)
      if (experiment.variants) {
        for (const variant of experiment.variants) {
          if (variant.isControl && variant.variantId !== variantId) {
            await storage.updateVariantTrafficAllocation(
              experimentId,
              variant.variantId,
              variant.trafficAllocation,
              false // not control anymore
            );
          }
        }
      }
    }
    
    // Update the variant in the experiment
    const updatedVariant = await storage.updateVariantTrafficAllocation(
      experimentId,
      variantId,
      validatedData.trafficAllocation,
      validatedData.isControl
    );
    
    if (!updatedVariant) {
      return res.status(404).json({ error: 'Experiment variant not found' });
    }
    
    return res.json(updatedVariant);
  } catch (error) {
    logger.error('Failed to update variant in experiment', { error });
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid variant data', details: error.errors });
    }
    
    return res.status(500).json({ error: 'Failed to update variant in experiment' });
  }
});

// Remove a variant from an experiment
router.delete('/:id/variants/:variantId', async (req: Request, res: Response) => {
  try {
    const experimentId = parseInt(req.params.id);
    const variantId = parseInt(req.params.variantId);
    
    if (isNaN(experimentId) || isNaN(variantId)) {
      return res.status(400).json({ error: 'Invalid ID' });
    }
    
    // Check if the variant is the control variant
    const experiment = await storage.getPromptExperimentWithVariants(experimentId);
    
    if (!experiment) {
      return res.status(404).json({ error: 'Prompt experiment not found' });
    }
    
    let isControlVariant = false;
    if (experiment.variants) {
      const variant = experiment.variants.find(v => v.variantId === variantId);
      isControlVariant = variant?.isControl || false;
      
      // Don't allow removing the only variant
      if (experiment.variants.length === 1) {
        return res.status(400).json({ 
          error: 'Cannot remove the only variant', 
          message: 'An experiment must have at least one variant' 
        });
      }
      
      // Don't allow removing the control variant without specifying a new one
      if (isControlVariant) {
        return res.status(400).json({ 
          error: 'Cannot remove control variant', 
          message: 'Please set another variant as control before removing this one' 
        });
      }
    }
    
    // Remove the variant from the experiment
    const success = await storage.removeVariantFromExperiment(experimentId, variantId);
    
    if (!success) {
      return res.status(404).json({ error: 'Experiment variant not found' });
    }
    
    return res.json({ message: 'Variant removed from experiment' });
  } catch (error) {
    logger.error('Failed to remove variant from experiment', { error });
    return res.status(500).json({ error: 'Failed to remove variant from experiment' });
  }
});

// Get metrics for an experiment
router.get('/:id/metrics', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid experiment ID' });
    }
    
    const experiment = await storage.getPromptExperimentWithVariants(id);
    
    if (!experiment) {
      return res.status(404).json({ error: 'Prompt experiment not found' });
    }
    
    // Collect metrics for each variant in the experiment
    const metrics = [];
    if (experiment.variants) {
      for (const variant of experiment.variants) {
        const variantDetails = await storage.getPromptVariant(variant.variantId);
        const variantMetrics = await storage.getPromptMetrics(variant.variantId);
        
        metrics.push({
          variantId: variant.variantId,
          variantName: variantDetails?.name || `Variant ${variant.variantId}`,
          isControl: variant.isControl,
          trafficAllocation: variant.trafficAllocation,
          impressions: variantMetrics.length,
          handovers: variantMetrics.filter(m => m.handoverRecommended).length,
          averageResponseTime: variantMetrics.reduce((sum, m) => sum + m.responseTime, 0) / (variantMetrics.length || 1),
          metrics: variantMetrics
        });
      }
    }
    
    return res.json({
      experiment,
      metrics
    });
  } catch (error) {
    logger.error('Failed to get experiment metrics', { error });
    return res.status(500).json({ error: 'Failed to get experiment metrics' });
  }
});

export default router;