import { Router } from 'express';
import { Pool } from 'pg';
import openaiService from '../services/openai';

const router = Router();

// Helper function to get a connection pool
async function getPool(): Promise<Pool> {
  const { Pool } = require('pg');
  return new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });
}

// Prompt testing endpoint
router.post('/test', async (req, res) => {
  try {
    // Temporarily disable authentication check for testing
    // if (!req.session.userId) {
    //   return res.status(401).json({ error: 'Authentication required' });
    // }

    const { prompt, variables, customerScenario, testType } = req.body;
    
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Process the prompt template with variables
    let processedPrompt = prompt;
    if (variables && typeof variables === 'object') {
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
        processedPrompt = processedPrompt.replace(regex, String(value));
      });
    }

    let response;
    
    if (testType === 'handover') {
      try {
        // Generate handover dossier using OpenAI
        const handoverDossier = await openaiService.generateHandoverDossier(processedPrompt, customerScenario || '');
        response = {
          success: true,
          testType: 'handover',
          processedPrompt,
          handoverDossier,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        console.error('Error generating handover dossier:', error);
        // Fallback response in case of OpenAI error
        response = {
          success: false,
          testType: 'handover',
          processedPrompt,
          error: 'Failed to generate handover dossier',
          timestamp: new Date().toISOString()
        };
      }
    } else {
      try {
        // Generate AI response using OpenAI
        const aiResponse = await openaiService.generateAIResponse(processedPrompt, customerScenario);
        
        // Generate response analysis
        const analysis = await openaiService.generateResponseAnalysis(processedPrompt, customerScenario || '');
        
        response = {
          success: true,
          testType: 'chat',
          processedPrompt,
          aiResponse,
          customerScenario: customerScenario || null,
          analysis,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        console.error('Error generating AI response:', error);
        // Fallback response in case of OpenAI error
        response = {
          success: false,
          testType: 'chat',
          processedPrompt,
          error: 'Failed to generate AI response',
          timestamp: new Date().toISOString()
        };
      }
    }

    // Store the test result in database (optional)
    try {
      const pool = await getPool();
      await pool.query(
        'INSERT INTO prompt_tests (user_id, original_prompt, processed_prompt, ai_response, variables, test_type, customer_scenario, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())',
        [
          req.session.userId, 
          prompt, 
          processedPrompt, 
          JSON.stringify(response), 
          JSON.stringify(variables || {}),
          testType || 'chat',
          customerScenario || null
        ]
      );
    } catch (dbError) {
      console.warn('Could not store test result:', dbError);
      // Continue without storing - don't fail the request
    }
    
    res.json(response);

  } catch (error: any) {
    console.error('Prompt test error:', error);
    res.status(500).json({ 
      error: 'Failed to test prompt',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get prompt templates (library)
router.get('/templates', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Mock prompt templates (replace with database query)
    const templates = [
      {
        id: 1,
        name: "Automotive Sales Agent",
        description: "Professional automotive sales agent for customer interactions",
        template: "You are an automotive sales agent named {{Agent_Name}}, working for {{Dealership}}. Rewrite if off: Trim wordiness, boost empathy, fix compliance, or adjust tone to sound like a real salesperson (e.g., replace \"We strive to assist\" with \"We've got you covered!\").",
        variables: ["Agent_Name", "Dealership"],
        category: "sales",
        isActive: true
      },
      {
        id: 2,
        name: "Lead Qualification",
        description: "Qualify leads and gather customer requirements",
        template: "You are a lead qualification specialist for {{Company}}. Your goal is to understand the customer's needs for {{Vehicle_Type}} and budget range of {{Budget_Range}}. Ask qualifying questions and provide relevant options.",
        variables: ["Company", "Vehicle_Type", "Budget_Range"],
        category: "qualification",
        isActive: true
      },
      {
        id: 3,
        name: "Trade-in Specialist",
        description: "Handle trade-in inquiries and valuations",
        template: "You are a trade-in specialist at {{Dealership}}. Help customers understand the trade-in process for their {{Current_Vehicle}}. Provide guidance on valuation and next steps while building confidence in our appraisal process.",
        variables: ["Dealership", "Current_Vehicle"],
        category: "trade-in",
        isActive: true
      },
      {
        id: 4,
        name: "Service Follow-up",
        description: "Follow up with customers after service appointments",
        template: "You are following up with {{Customer_Name}} regarding their recent {{Service_Type}} service at {{Dealership}}. Thank them for their business and ensure their satisfaction with the service provided.",
        variables: ["Customer_Name", "Service_Type", "Dealership"],
        category: "service",
        isActive: true
      }
    ];

    res.json({ templates });

  } catch (error: any) {
    console.error('Error fetching prompt templates:', error);
    res.status(500).json({ error: 'Failed to fetch prompt templates' });
  }
});

// Save/update prompt template
router.post('/templates', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { name, description, template, variables, category } = req.body;
    
    if (!name || !template) {
      return res.status(400).json({ error: 'Name and template are required' });
    }

    // Mock save (replace with actual database insert)
    const newTemplate = {
      id: Date.now(), // Mock ID
      name,
      description: description || '',
      template,
      variables: variables || [],
      category: category || 'general',
      isActive: true,
      createdBy: req.session.userId,
      createdAt: new Date().toISOString()
    };

    res.json({ 
      success: true, 
      template: newTemplate,
      message: 'Template saved successfully'
    });

  } catch (error: any) {
    console.error('Error saving prompt template:', error);
    res.status(500).json({ error: 'Failed to save prompt template' });
  }
});

// Get test history
router.get('/history', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    try {
      const pool = await getPool();
      const result = await pool.query(
        'SELECT id, original_prompt, processed_prompt, ai_response, variables, test_type, customer_scenario, created_at FROM prompt_tests WHERE user_id = $1 ORDER BY created_at DESC LIMIT 50',
        [req.session.userId]
      );

      res.json({
        tests: result.rows
      });
    } catch (dbError) {
      console.warn('Database query failed, returning empty history:', dbError);
      res.json({ tests: [] });
    }

  } catch (error: any) {
    console.error('Error fetching prompt tests:', error);
    res.status(500).json({ error: 'Failed to fetch prompt tests' });
  }
});

export default router;