import { Router } from "express";
import { db } from "../db";
import { savedPrompts, insertSavedPromptSchema } from "@shared/schema";
import { isAuthenticated } from "../replitAuth";
import { eq, and, or, desc } from "drizzle-orm";
import { z } from "zod";

const router = Router();

// Create a new saved prompt
router.post("/", isAuthenticated, async (req: any, res) => {
  try {
    const validatedData = insertSavedPromptSchema.parse(req.body);
    
    // Add the user ID from the authenticated session
    const userId = req.user?.claims?.sub;
    
    const [savedPrompt] = await db
      .insert(savedPrompts)
      .values({
        ...validatedData,
        userId,
        updatedAt: new Date()
      })
      .returning();
    
    res.status(201).json(savedPrompt);
  } catch (error: any) {
    console.error("Error saving prompt:", error);
    res.status(400).json({ message: "Failed to save prompt", error: error.message });
  }
});

// Get a list of saved prompts (filtered by user and/or public)
router.get("/", isAuthenticated, async (req: any, res) => {
  try {
    const userId = req.user?.claims?.sub;
    const { isPublic, dealershipId } = req.query;
    
    let query = db.select().from(savedPrompts);
    
    // Filter based on query parameters
    if (dealershipId) {
      // If dealership ID is provided, get prompts for that dealership
      query = query.where(eq(savedPrompts.dealershipId, Number(dealershipId)));
    } else {
      // Otherwise, get user's prompts and public prompts
      query = query.where(
        or(
          eq(savedPrompts.userId, userId),
          eq(savedPrompts.isPublic, true)
        )
      );
    }
    
    // Optional filter for public/private
    if (isPublic !== undefined) {
      query = query.where(eq(savedPrompts.isPublic, isPublic === 'true'));
    }
    
    // Order by most recently updated
    query = query.orderBy(desc(savedPrompts.updatedAt));
    
    const results = await query;
    res.json(results);
  } catch (error: any) {
    console.error("Error fetching saved prompts:", error);
    res.status(500).json({ message: "Failed to fetch saved prompts", error: error.message });
  }
});

// Get a specific saved prompt by ID
router.get("/:id", isAuthenticated, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: "Invalid prompt ID" });
    }
    
    const [prompt] = await db
      .select()
      .from(savedPrompts)
      .where(eq(savedPrompts.id, id));
    
    if (!prompt) {
      return res.status(404).json({ message: "Prompt not found" });
    }
    
    res.json(prompt);
  } catch (error: any) {
    console.error("Error fetching saved prompt:", error);
    res.status(500).json({ message: "Failed to fetch saved prompt", error: error.message });
  }
});

// Update a saved prompt
router.put("/:id", isAuthenticated, async (req: any, res) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: "Invalid prompt ID" });
    }
    
    const userId = req.user?.claims?.sub;
    
    // Get the existing prompt
    const [existingPrompt] = await db
      .select()
      .from(savedPrompts)
      .where(eq(savedPrompts.id, id));
    
    if (!existingPrompt) {
      return res.status(404).json({ message: "Prompt not found" });
    }
    
    // Check if user owns this prompt
    if (existingPrompt.userId !== userId) {
      return res.status(403).json({ message: "You don't have permission to update this prompt" });
    }
    
    const validatedData = insertSavedPromptSchema.partial().parse(req.body);
    
    const [updatedPrompt] = await db
      .update(savedPrompts)
      .set({
        ...validatedData,
        updatedAt: new Date()
      })
      .where(eq(savedPrompts.id, id))
      .returning();
    
    res.json(updatedPrompt);
  } catch (error: any) {
    console.error("Error updating saved prompt:", error);
    res.status(400).json({ message: "Failed to update prompt", error: error.message });
  }
});

// Delete a saved prompt
router.delete("/:id", isAuthenticated, async (req: any, res) => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      return res.status(400).json({ message: "Invalid prompt ID" });
    }
    
    const userId = req.user?.claims?.sub;
    
    // Get the existing prompt
    const [existingPrompt] = await db
      .select()
      .from(savedPrompts)
      .where(eq(savedPrompts.id, id));
    
    if (!existingPrompt) {
      return res.status(404).json({ message: "Prompt not found" });
    }
    
    // Check if user owns this prompt
    if (existingPrompt.userId !== userId) {
      return res.status(403).json({ message: "You don't have permission to delete this prompt" });
    }
    
    await db
      .delete(savedPrompts)
      .where(eq(savedPrompts.id, id));
    
    res.status(204).end();
  } catch (error: any) {
    console.error("Error deleting saved prompt:", error);
    res.status(500).json({ message: "Failed to delete prompt", error: error.message });
  }
});

export default router;