import express from 'express';
import { OpenAI } from 'openai';

const router = express.Router();

// Direct prompt testing endpoint - simplified version to ensure reliability
router.post('/', async (req, res) => {
  try {
    // Always set the content type to application/json
    res.setHeader('Content-Type', 'application/json');
    
    const { systemPrompt, customerMessage } = req.body;
    
    if (!systemPrompt || !customerMessage) {
      return res.status(400).json({ 
        error: 'Invalid request', 
        message: 'Both systemPrompt and customerMessage are required' 
      });
    }
    
    // Check for API key
    if (!process.env.OPENAI_API_KEY) {
      return res.status(500).json({ 
        error: 'API configuration error', 
        message: 'OpenAI API key is not configured'
      });
    }
    
    // Initialize OpenAI client
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    // Call the API
    try {
      const completion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: customerMessage
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      });
      
      // Return the response
      return res.status(200).json({
        response: completion.choices[0].message.content,
        usage: completion.usage
      });
    } catch (openaiError) {
      console.error('OpenAI API error:', openaiError);
      return res.status(500).json({
        error: 'OpenAI API error',
        message: openaiError instanceof Error ? openaiError.message : 'Error calling OpenAI API'
      });
    }
  } catch (error) {
    console.error('Error in direct prompt test:', error);
    return res.status(500).json({
      error: 'Server error',
      message: error instanceof Error ? error.message : 'Unknown server error'
    });
  }
});

export default router;