import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import logger from '../utils/logger';
import { apiKeyAuth } from '../middleware/auth';

const router = Router();

// Apply API key authentication to all routes
router.use(apiKeyAuth);

// Get all prompt variants for a dealership
router.get('/', async (req: Request, res: Response) => {
  try {
    const dealershipId = parseInt(req.query.dealershipId as string);
    
    if (isNaN(dealershipId)) {
      return res.status(400).json({ error: 'Invalid dealership ID' });
    }
    
    const includeInactive = req.query.includeInactive === 'true';
    const variants = await storage.getPromptVariantsByDealership(dealershipId, includeInactive);
    
    return res.json(variants);
  } catch (error) {
    logger.error('Failed to get prompt variants', { error });
    return res.status(500).json({ error: 'Failed to get prompt variants' });
  }
});

// Get a single prompt variant
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid variant ID' });
    }
    
    const variant = await storage.getPromptVariant(id);
    
    if (!variant) {
      return res.status(404).json({ error: 'Prompt variant not found' });
    }
    
    return res.json(variant);
  } catch (error) {
    logger.error('Failed to get prompt variant', { error });
    return res.status(500).json({ error: 'Failed to get prompt variant' });
  }
});

// Create a new prompt variant
router.post('/', async (req: Request, res: Response) => {
  try {
    const schema = z.object({
      dealershipId: z.number(),
      name: z.string().min(1),
      content: z.string().min(10),
      isActive: z.boolean().default(true),
      isControl: z.boolean().default(false)
    });
    
    const validatedData = schema.parse(req.body);
    
    // If this is set as control, update other variants for this dealership
    if (validatedData.isControl) {
      try {
        const variants = await storage.getPromptVariantsByDealership(validatedData.dealershipId);
        
        // Update any existing control variants to not be control
        for (const variant of variants) {
          if (variant.isControl) {
            await storage.updatePromptVariant(variant.id, {
              isControl: false
            });
          }
        }
      } catch (error) {
        logger.error('Failed to update control variants', { error });
        // Continue with creation regardless
      }
    }
    
    // Map the content field to promptTemplate as required by our schema
    const newVariant = await storage.createPromptVariant({
      name: validatedData.name,
      promptTemplate: validatedData.content,
      dealershipId: validatedData.dealershipId,
      isActive: validatedData.isActive,
      isControl: validatedData.isControl
    });
    
    return res.status(201).json(newVariant);
  } catch (error) {
    logger.error('Failed to create prompt variant', { error });
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid variant data', details: error.errors });
    }
    
    return res.status(500).json({ error: 'Failed to create prompt variant' });
  }
});

// Update a prompt variant
router.patch('/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid variant ID' });
    }
    
    const schema = z.object({
      name: z.string().min(1).optional(),
      content: z.string().min(10).optional(),
      isActive: z.boolean().optional(),
      isControl: z.boolean().optional(),
      dealershipId: z.number().optional()
    });
    
    const validatedData = schema.parse(req.body);
    
    // Prepare update data with correct field mappings
    const updateData: any = {
      ...(validatedData.name && { name: validatedData.name }),
      ...(validatedData.content && { promptTemplate: validatedData.content }),
      ...(validatedData.isActive !== undefined && { isActive: validatedData.isActive }),
      ...(validatedData.isControl !== undefined && { isControl: validatedData.isControl }),
      ...(validatedData.dealershipId && { dealershipId: validatedData.dealershipId })
    };
    
    // If this is set as control, update other variants for this dealership
    if (validatedData.isControl) {
      try {
        // First get this variant to know its dealership
        const variant = await storage.getPromptVariant(id);
        
        if (variant) {
          const dealershipId = validatedData.dealershipId || variant.dealershipId;
          const variants = await storage.getPromptVariantsByDealership(dealershipId);
          
          // Update any existing control variants to not be control
          for (const v of variants) {
            if (v.isControl && v.id !== id) {
              await storage.updatePromptVariant(v.id, {
                isControl: false
              });
            }
          }
        }
      } catch (error) {
        logger.error('Failed to update control variants', { error });
        // Continue with update regardless
      }
    }
    
    const updatedVariant = await storage.updatePromptVariant(id, updateData);
    
    if (!updatedVariant) {
      return res.status(404).json({ error: 'Prompt variant not found' });
    }
    
    return res.json(updatedVariant);
  } catch (error) {
    logger.error('Failed to update prompt variant', { error });
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid variant data', details: error.errors });
    }
    
    return res.status(500).json({ error: 'Failed to update prompt variant' });
  }
});

// Set a variant as the control variant for a dealership
router.post('/:id/set-control', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    
    if (isNaN(id)) {
      return res.status(400).json({ error: 'Invalid variant ID' });
    }
    
    const schema = z.object({
      dealershipId: z.number()
    });
    
    const validatedData = schema.parse(req.body);
    
    const updatedVariant = await storage.setControlVariant(id, validatedData.dealershipId);
    
    if (!updatedVariant) {
      return res.status(404).json({ error: 'Prompt variant not found' });
    }
    
    return res.json(updatedVariant);
  } catch (error) {
    logger.error('Failed to set control variant', { error });
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid data', details: error.errors });
    }
    
    return res.status(500).json({ error: 'Failed to set control variant' });
  }
});

export default router;