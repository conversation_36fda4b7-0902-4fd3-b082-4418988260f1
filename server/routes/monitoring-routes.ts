import express from 'express';
import { monitoring } from '../services/monitoring';

const router = express.Router();

// Get current metrics
router.get('/', (req, res) => {
  // Only allow from internal network or authenticated users
  if (req.ip.startsWith('127.0.0.') || req.ip === '::1' || req.isAuthenticated()) {
    res.json(monitoring.getMetrics());
  } else {
    res.status(403).json({ message: 'Access denied' });
  }
});

// Reset metrics
router.post('/reset', (req, res) => {
  // Only allow from internal network or authenticated admins
  if (req.ip.startsWith('127.0.0.') || req.ip === '::1' || 
      (req.isAuthenticated() && req.user.role === 'admin')) {
    monitoring.resetMetrics();
    res.json({ message: 'Metrics reset successfully' });
  } else {
    res.status(403).json({ message: 'Access denied' });
  }
});

export default router;