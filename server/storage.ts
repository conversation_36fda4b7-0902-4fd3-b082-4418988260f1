import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { users, User, InsertUser } from "@shared/schema";
import { eq } from "drizzle-orm";
import session from "express-session";
import connectPg from "connect-pg-simple";
import { Pool } from "pg";

// Database connection for Drizzle ORM with improved configuration
const connectionString = process.env.DATABASE_URL!;
const client = postgres(connectionString, {
  max: 10, // Maximum concurrent connections
  idle_timeout: 30, // Close idle connections after 30 seconds
  connect_timeout: 10, // Connection timeout in seconds
  max_lifetime: 60 * 30, // Connection lifetime in seconds (30 minutes)
  ssl: true, // Enable SSL for cloud database providers
});
const db = drizzle(client);

// Create a pg Pool for session store with better configuration
// connect-pg-simple requires a pg Pool instance specifically
const pgPool = new Pool({
  connectionString: connectionString,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 5000, // Increased timeout for connections
  // Add retry logic
  ssl: {
    rejectUnauthorized: false // Needed for some cloud PostgreSQL providers
  },
});

// Set up improved error handler for the pool
pgPool.on('error', (err) => {
  console.error('PostgreSQL client error:', err.message);
  // Log additional details for troubleshooting
  if (err.message.includes('timeout')) {
    console.error('Connection timeout detected. This may be due to network issues or database overload.');
  } else if (err.message.includes('terminated')) {
    console.error('Connection terminated. The database server may have closed the connection.');
  }
});

// Test the pool connection with retry mechanism
(async () => {
  let retries = 5;
  let connected = false;
  
  while (retries > 0 && !connected) {
    try {
      const testClient = await pgPool.connect();
      console.log('PostgreSQL connection test successful');
      testClient.release();
      connected = true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`PostgreSQL connection attempt failed (${retries} retries left):`, errorMessage);
      retries--;
      
      if (retries > 0) {
        // Wait before retrying (exponential backoff)
        const delay = (6 - retries) * 1000; // Increasing delay with each retry
        console.log(`Retrying connection in ${delay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        console.error('All PostgreSQL connection attempts failed');
      }
    }
  }
})();

// Session store setup
const PostgresSessionStore = connectPg(session);

export interface IStorage {
  // User management
  createUser(user: Omit<InsertUser, "id">): Promise<User>;
  getUser(id: number): Promise<User | null>;
  getUserByUsername(username: string): Promise<User | null>;
  
  // Session store
  sessionStore: session.Store;
}

// PostgreSQL storage implementation
export class PostgresStorage implements IStorage {
  sessionStore: session.Store;
  
  constructor() {
    try {
      console.log('Initializing PostgreSQL session store');
      this.sessionStore = new PostgresSessionStore({
        createTableIfMissing: true,
        pool: pgPool,
        tableName: 'session', // Explicitly name the session table
        pruneSessionInterval: 60 * 15 // Prune expired sessions every 15 minutes
      });
      console.log('PostgreSQL session store initialized successfully');
    } catch (error) {
      console.error('Failed to initialize PostgreSQL session store:', error);
      throw error;
    }
  }

  async createUser(userData: Omit<InsertUser, "id">): Promise<User> {
    // Insert user into database
    const [user] = await db.insert(users).values(userData).returning();
    return user;
  }

  async getUser(id: number): Promise<User | null> {
    // Get user by ID
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result.length > 0 ? result[0] : null;
  }

  async getUserByUsername(username: string): Promise<User | null> {
    // Get user by username (case-sensitive)
    const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
    return result.length > 0 ? result[0] : null;
  }
}

// Singleton storage instance
export const storage = new PostgresStorage();