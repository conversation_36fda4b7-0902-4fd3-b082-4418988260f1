import sgMail from '@sendgrid/mail';
import { logger } from '../logger';

/**
 * Check if SendGrid is configured
 */
function isSendGridConfigured(): boolean {
  return !!process.env.SENDGRID_API_KEY;
}

/**
 * Initialize SendGrid with API key
 */
function initializeSendGrid(): void {
  if (isSendGridConfigured()) {
    sgMail.setApiKey(process.env.SENDGRID_API_KEY!);
  } else {
    logger.warn('SendGrid API key not found. Email functionality will not work.');
  }
}

// Initialize SendGrid when this module is loaded
initializeSendGrid();

/**
 * Email message interface
 */
export interface EmailMessage {
  to: string | string[];
  from?: string;
  subject: string;
  text: string;
  html: string;
}

/**
 * Send an email using SendGrid
 * 
 * @param message - The email message to send
 * @returns Object with success status and error message if applicable
 */
export async function sendEmail(message: EmailMessage): Promise<{ success: boolean; error?: string }> {
  try {
    // Validate SendGrid configuration
    if (!isSendGridConfigured()) {
      logger.error('Cannot send email: SendGrid API key is not configured');
      return {
        success: false,
        error: 'Email service is not configured. Please set SENDGRID_API_KEY.',
      };
    }

    // Set default sender if not provided
    const emailMessage = {
      ...message,
      from: message.from || process.env.EMAIL_FROM || '<EMAIL>',
    };

    // Send the email
    await sgMail.send(emailMessage);
    
    logger.info(`Email sent successfully to ${Array.isArray(message.to) ? message.to.join(', ') : message.to}`);
    
    return { success: true };
  } catch (error) {
    logger.error('Failed to send email:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error sending email',
    };
  }
}