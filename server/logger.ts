/**
 * Simple logger module to provide consistent logging throughout the application
 */

// Define log levels
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

// Logger class with basic functionality
class Logger {
  private logLevel: LogLevel = LogLevel.INFO;

  // Set the current log level
  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  // Log an error message with optional data and context
  error(message: string, err?: any, context?: any): void {
    console.error(`[ERROR] ${new Date().toISOString()}:`, message, err ? err : '', context ? context : '');
  }

  // Log a warning message with optional context
  warn(message: string, context?: any): void {
    if ([LogLevel.WARN, LogLevel.INFO, LogLevel.DEBUG].includes(this.logLevel)) {
      console.warn(`[WARN] ${new Date().toISOString()}:`, message, context ? context : '');
    }
  }

  // Log an info message with optional context
  info(message: string, context?: any): void {
    if ([LogLevel.INFO, LogLevel.DEBUG].includes(this.logLevel)) {
      console.info(`[INFO] ${new Date().toISOString()}:`, message, context ? context : '');
    }
  }

  // Log a debug message with optional context
  debug(message: string, context?: any): void {
    if (this.logLevel === LogLevel.DEBUG) {
      console.debug(`[DEBUG] ${new Date().toISOString()}:`, message, context ? context : '');
    }
  }
}

// Export a singleton instance
export const logger = new Logger();

// Set log level based on environment
if (process.env.NODE_ENV === 'production') {
  logger.setLogLevel(LogLevel.WARN);
} else if (process.env.DEBUG === 'true') {
  logger.setLogLevel(LogLevel.DEBUG);
}