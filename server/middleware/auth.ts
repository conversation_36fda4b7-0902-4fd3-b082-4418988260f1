import { Request, Response, NextFunction } from "express";
import { storage } from "../storage";
import { Session } from "express-session";
import { User } from "@shared/schema";

// Extend the Session interface to include our user type
declare module "express-session" {
  interface Session {
    userId?: number;
    user?: User;
    role?: string;
  }
}

// Define type for authenticated request
export interface AuthenticatedRequest extends Request {
  dealershipId?: number;
  apiKey?: string;
}

// Middleware to check for valid API key with enhanced security
export async function apiKeyAuth(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) {
  const apiKey = req.headers["x-api-key"] as string;
  const clientIp = req.ip || req.headers['x-forwarded-for'] as string || 'unknown';
  const userAgent = req.headers['user-agent'] || 'unknown';
  const requestPath = req.path;

  // Comprehensive security checks for API key
  if (!apiKey) {
    // Log missing API key attempts for security monitoring
    storage.logSecurityEvent({
      type: 'missing_api_key',
      ip: clientIp,
      userAgent,
      path: requestPath,
      timestamp: new Date()
    }).catch(err => console.error('Failed to log security event:', err));
    
    return res.status(401).json({
      error: "authentication_failed",
      message: "Authentication failed: API key is required",
    });
  }
  
  // Validate API key format (ryk_* format) with strict pattern matching
  const keyFormatRegex = /^ryk_[a-zA-Z0-9]{32}$/;
  if (!keyFormatRegex.test(apiKey)) {
    // Log invalid format attempts for security monitoring
    storage.logSecurityEvent({
      type: 'invalid_api_key_format',
      ip: clientIp,
      userAgent,
      path: requestPath,
      timestamp: new Date(),
      apiKeyPrefix: apiKey.substring(0, 6) // Log only prefix for security
    }).catch(err => console.error('Failed to log security event:', err));
    
    return res.status(401).json({
      error: "invalid_api_key_format",
      message: "Authentication failed: Invalid API key format",
    });
  }

  try {
    const validApiKey = await storage.verifyApiKey(apiKey);

    if (!validApiKey) {
      // Log invalid API key attempts
      storage.logSecurityEvent({
        type: 'invalid_api_key',
        ip: clientIp,
        userAgent,
        path: requestPath,
        timestamp: new Date(),
        apiKeyPrefix: apiKey.substring(0, 6) // Log only prefix for security
      }).catch(err => console.error('Failed to log security event:', err));
      
      return res.status(401).json({
        error: "invalid_api_key",
        message: "Authentication failed: Invalid API key",
      });
    }
    
    // Check for API key status issues
    const now = new Date();
    
    // Disabled key check
    if (validApiKey.isActive === false) {
      storage.logSecurityEvent({
        type: 'disabled_api_key',
        ip: clientIp,
        userAgent,
        path: requestPath,
        timestamp: now,
        dealershipId: validApiKey.dealershipId
      }).catch(err => console.error('Failed to log security event:', err));
      
      return res.status(401).json({
        error: "disabled_api_key",
        message: "Authentication failed: API key has been disabled",
      });
    }

    // Check for unusual access patterns (different IPs, unusual times, etc.)
    // This is a simple implementation - can be expanded with more sophisticated detection
    const isUnusualAccess = await detectUnusualAccess(validApiKey.id, clientIp, userAgent);
    if (isUnusualAccess) {
      // Just log the unusual access but still allow it (alternatively could block or require additional verification)
      storage.logSecurityEvent({
        type: 'unusual_api_key_access',
        ip: clientIp,
        userAgent,
        path: requestPath,
        timestamp: now,
        dealershipId: validApiKey.dealershipId,
        apiKeyId: validApiKey.id,
        severity: 'warning'
      }).catch(err => console.error('Failed to log security event:', err));
    }
    
    // Update last used timestamp for the API key
    storage.updateApiKeyLastUsed(validApiKey.id, now)
      .catch(err => console.error('Failed to update API key last used timestamp:', err));

    // Add dealership ID to request for use in route handlers
    req.dealershipId = validApiKey.dealershipId;
    req.apiKey = apiKey;

    next();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown authentication error';
    console.error("Authentication error:", error);
    
    // Log authentication errors
    storage.logSecurityEvent({
      type: 'auth_error',
      ip: clientIp,
      userAgent,
      path: requestPath,
      timestamp: new Date(),
      error: errorMessage
    }).catch(err => console.error('Failed to log security event:', err));
    
    return res.status(500).json({
      error: "server_error",
      message: "An error occurred during authentication",
      detail: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
}

// Helper function to detect unusual API key access patterns
async function detectUnusualAccess(apiKeyId: number, ip: string, userAgent: string): Promise<boolean> {
  try {
    // Get recent access history for this API key
    const accessHistory = await storage.getApiKeyAccessHistory(apiKeyId);
    
    if (!accessHistory || accessHistory.length === 0) {
      // No history to compare against
      return false;
    }
    
    // Check for IP address changes
    const knownIps = new Set(accessHistory.map(h => h.ip));
    const isNewIp = !knownIps.has(ip);
    
    // Check for user agent changes
    const knownUserAgents = new Set(accessHistory.map(h => h.userAgent));
    const isNewUserAgent = !knownUserAgents.has(userAgent);
    
    // Return true if there are unusual patterns
    return isNewIp || isNewUserAgent;
  } catch (error) {
    console.error('Error in unusual access detection:', error);
    // Default to false on error to avoid blocking legitimate access
    return false;
  }
}

// Middleware for session authentication (for dashboard access)
export function sessionAuth(
  req: Request,
  res: Response,
  next: NextFunction
) {
  if (!req.session || !req.session.userId) {
    return res.status(401).json({
      message: "Authentication failed: Please log in",
    });
  }

  next();
}

// Role-based access control middleware
export function requireRole(roles: string | string[]) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const user = await storage.getUser(req.session?.userId);
    
    if (!user || !user.isVerified) {
      return res.status(403).json({
        message: "Please verify your email first",
      });
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(user.role)) {
      return res.status(403).json({
        message: `Access denied: Required role(s): ${allowedRoles.join(', ')}`,
      });
    }

    next();
  };
}

// Admin only middleware (shorthand)
export const adminOnly = requireRole('admin');

// Manager only middleware
export const managerOnly = requireRole(['admin', 'manager']);

// Verified user middleware
export function requireVerified(req: Request, res: Response, next: NextFunction) {
  const user = req.user as any;
  
  if (!user || !user.isVerified) {
    return res.status(403).json({
      message: "Please verify your email first",
    });
  }

  next();
}
