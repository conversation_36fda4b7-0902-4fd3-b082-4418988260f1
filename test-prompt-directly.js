// Simple script to test the system prompt
import OpenAI from 'openai';
import { DEFAULT_SYSTEM_PROMPT } from './server/services/system-prompts/default.js';
import dotenv from 'dotenv';

dotenv.config();

// Sample inventory
const SAMPLE_INVENTORY = `
- 2024 Toyota RAV4 Hybrid XLE (Silver): AWD, 2.5L Hybrid Engine, 41 MPG City/38 Highway, Leather Seats, Sunroof, $34,995
- 2023 Honda Civic EX (Blue): FWD, 1.5L Turbo Engine, 33 MPG City/42 Highway, Android Auto/Apple CarPlay, $26,450
- 2024 Ford F-150 XLT (Black): 4x4, 3.5L EcoBoost, Leather Interior, Navigation, Towing Package, $48,995
- 2022 Chevrolet Equinox LT (White): FWD, 1.5L Turbo, Heated Seats, Remote Start, Teen Driver Technology, $27,500
- 2023 Tesla Model Y (Red): AWD, Dual Motor, 330 Mile Range, Premium Interior, 15" Touchscreen, Autopilot, $59,990
`;

// Test message
const TEST_MESSAGE = "Hi, I'm looking for an SUV that gets good gas mileage. What do you recommend?";

async function testPrompt() {
  // Check if API key exists
  if (!process.env.OPENAI_API_KEY) {
    console.log("Error: OpenAI API key not found in environment variables");
    return;
  }
  
  // Initialize OpenAI client
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
  
  // Format the system prompt
  const formattedPrompt = DEFAULT_SYSTEM_PROMPT
    .replace('[ARG-Agent Name]', 'Rylie')
    .replace('[ARG-Employer Name]', 'PureCars Dealership')
    .replace('[ARG-Information About Employer]', 'A family-owned dealership since 1987')
    .replace('[ARG-Products]', 'New and certified pre-owned vehicles')
    .replace('[ARG-Employer Contact Details]', 'Phone: ************, Email: <EMAIL>')
    .replace('[ARG-Name]', 'Alex')
    .replace('[ARG-Contact Details]', 'Phone: ************')
    .replace('[INPUT-Product Inventory]', SAMPLE_INVENTORY)
    .replace('[INPUT-CUSTOMER NAME]', 'Customer')
    .replace('[INPUT-CONVERSATION]', 'No previous conversation');
  
  console.log("Sending test message to OpenAI...");
  
  try {
    // Call OpenAI API
    const response = await openai.chat.completions.create({
      model: 'gpt-4-turbo-preview',
      messages: [
        {
          role: 'system',
          content: formattedPrompt
        },
        {
          role: 'user',
          content: TEST_MESSAGE
        }
      ],
      temperature: 0.7,
      max_tokens: 800,
      response_format: { type: 'json_object' }
    });
    
    // Parse and display the response
    const responseContent = response.choices[0].message.content;
    if (responseContent) {
      try {
        const jsonResponse = JSON.parse(responseContent);
        
        console.log("\n=== AI RESPONSE (Updated System Prompt) ===\n");
        console.log(jsonResponse.answer);
        
        console.log("\n=== RESPONSE ANALYTICS ===");
        console.log(`Sales Readiness: ${jsonResponse.sales_readiness}`);
        console.log(`Quick Insights: ${jsonResponse.quick_insights}`);
        
      } catch (error) {
        console.log("Error parsing JSON response:", error);
        console.log("Raw response:", responseContent);
      }
    }
  } catch (error) {
    console.log("Error calling OpenAI API:", error);
  }
}

// Run the test
testPrompt();