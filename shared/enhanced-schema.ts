import { pgTable, serial, text, varchar, timestamp, boolean, integer, json, unique, foreignKey, index } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// ===== Base Tables =====

// Dealerships table - the core of our multi-tenant system
export const dealerships = pgTable('dealerships', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  subdomain: varchar('subdomain', { length: 100 }).notNull().unique(),
  contact_email: varchar('contact_email', { length: 255 }).notNull(),
  contact_phone: varchar('contact_phone', { length: 50 }),
  address: text('address'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 50 }),
  zip: varchar('zip', { length: 20 }),
  website: varchar('website', { length: 255 }),
  description: text('description'),
  // Branding fields
  logo_url: varchar('logo_url', { length: 255 }),
  primary_color: varchar('primary_color', { length: 20 }).default('#000000'),
  secondary_color: varchar('secondary_color', { length: 20 }).default('#ffffff'),
  accent_color: varchar('accent_color', { length: 20 }).default('#4f46e5'),
  font_family: varchar('font_family', { length: 100 }).default('Inter, system-ui, sans-serif'),
  // Persona settings
  persona_name: varchar('persona_name', { length: 100 }).default('Rylie'),
  persona_tone: varchar('persona_tone', { length: 50 }).default('friendly'), // friendly, professional, casual, formal
  persona_template: text('persona_template'), // Base persona template to use
  welcome_message: text('welcome_message'), // Custom welcome message for new conversations
  // Operational settings
  active: boolean('active').default(true),
  settings: json('settings').$type<Record<string, any>>().default({}),
  created_at: timestamp('created_at').defaultNow(),
  updated_at: timestamp('updated_at').defaultNow(),
});

// User roles enum
export const userRoles = ['super_admin', 'dealership_admin', 'manager', 'user'] as const;
export type UserRole = typeof userRoles[number];

// Users table with dealership_id foreign key
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  username: varchar('username', { length: 100 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  password_hash: varchar('password_hash', { length: 255 }),
  name: varchar('name', { length: 255 }),
  role: varchar('role', { length: 50 }).$type<UserRole>().default('user'),
  dealership_id: integer('dealership_id').references(() => dealerships.id, { onDelete: 'set null' }),
  is_verified: boolean('is_verified').default(false),
  verification_token: varchar('verification_token', { length: 255 }),
  reset_token: varchar('reset_token', { length: 255 }),
  reset_token_expiry: timestamp('reset_token_expiry'),
  last_login: timestamp('last_login'),
  created_at: timestamp('created_at').defaultNow(),
  updated_at: timestamp('updated_at').defaultNow(),
}, table => ({
  // Index to improve query performance for users by dealership
  dealershipIdx: index('user_dealership_idx').on(table.dealership_id),
  // Index for email lookups during authentication
  emailIdx: index('user_email_idx').on(table.email),
}));

// ===== Prompt Management Tables =====

// System prompts table with dealership_id for isolation
export const systemPrompts = pgTable('system_prompts', {
  id: serial('id').primaryKey(),
  dealership_id: integer('dealership_id').notNull().references(() => dealerships.id, { onDelete: 'cascade' }),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  template: text('template').notNull(),
  variables: json('variables').$type<string[]>().default([]),
  category: varchar('category', { length: 100 }).default('general'),
  is_active: boolean('is_active').default(true),
  is_default: boolean('is_default').default(false),
  created_by: integer('created_by').references(() => users.id),
  created_at: timestamp('created_at').defaultNow(),
  updated_at: timestamp('updated_at').defaultNow(),
}, table => ({
  dealershipIdx: index('prompt_dealership_idx').on(table.dealership_id),
}));

// Prompt variables specific to each dealership
export const dealershipVariables = pgTable('dealership_variables', {
  id: serial('id').primaryKey(),
  dealership_id: integer('dealership_id').notNull().references(() => dealerships.id, { onDelete: 'cascade' }),
  name: varchar('name', { length: 100 }).notNull(),
  value: text('value').notNull(),
  description: text('description'),
  created_at: timestamp('created_at').defaultNow(),
  updated_at: timestamp('updated_at').defaultNow(),
}, table => ({
  // Ensure variable names are unique per dealership
  uniqueVarName: unique('unique_var_name').on(table.dealership_id, table.name),
  dealershipIdx: index('var_dealership_idx').on(table.dealership_id),
}));

// ===== Vehicle Inventory Tables =====

// Vehicle inventory table with dealership_id for isolation
export const vehicles = pgTable('vehicles', {
  id: serial('id').primaryKey(),
  dealership_id: integer('dealership_id').notNull().references(() => dealerships.id, { onDelete: 'cascade' }),
  vin: varchar('vin', { length: 17 }).notNull(),
  make: varchar('make', { length: 100 }).notNull(),
  model: varchar('model', { length: 100 }).notNull(),
  year: integer('year').notNull(),
  trim: varchar('trim', { length: 100 }),
  exterior_color: varchar('exterior_color', { length: 100 }),
  interior_color: varchar('interior_color', { length: 100 }),
  mileage: integer('mileage'),
  price: integer('price'),
  msrp: integer('msrp'),
  condition: varchar('condition', { length: 50 }),
  description: text('description'),
  features: json('features').$type<string[]>().default([]),
  images: json('images').$type<string[]>().default([]),
  status: varchar('status', { length: 50 }).default('available'),
  created_at: timestamp('created_at').defaultNow(),
  updated_at: timestamp('updated_at').defaultNow(),
}, table => ({
  // VIN should be unique per dealership
  uniqueVin: unique('unique_vin').on(table.dealership_id, table.vin),
  dealershipIdx: index('vehicle_dealership_idx').on(table.dealership_id),
}));

// ===== Customer Interaction Tables =====

// Customers table with dealership_id for isolation
export const customers = pgTable('customers', {
  id: serial('id').primaryKey(),
  dealership_id: integer('dealership_id').notNull().references(() => dealerships.id, { onDelete: 'cascade' }),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }),
  phone: varchar('phone', { length: 50 }),
  address: text('address'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 50 }),
  zip: varchar('zip', { length: 20 }),
  notes: text('notes'),
  created_at: timestamp('created_at').defaultNow(),
  updated_at: timestamp('updated_at').defaultNow(),
}, table => ({
  dealershipIdx: index('customer_dealership_idx').on(table.dealership_id),
}));

// Conversations table with dealership_id for isolation
export const conversations = pgTable('conversations', {
  id: serial('id').primaryKey(),
  dealership_id: integer('dealership_id').notNull().references(() => dealerships.id, { onDelete: 'cascade' }),
  customer_id: integer('customer_id').references(() => customers.id, { onDelete: 'set null' }),
  started_by: integer('started_by').references(() => users.id),
  channel: varchar('channel', { length: 50 }).default('web'), // web, email, sms, etc.
  status: varchar('status', { length: 50 }).default('active'), // active, closed, escalated
  subject: varchar('subject', { length: 255 }),
  created_at: timestamp('created_at').defaultNow(),
  updated_at: timestamp('updated_at').defaultNow(),
}, table => ({
  dealershipIdx: index('conversation_dealership_idx').on(table.dealership_id),
}));

// Messages within conversations
export const messages = pgTable('messages', {
  id: serial('id').primaryKey(),
  conversation_id: integer('conversation_id').notNull().references(() => conversations.id, { onDelete: 'cascade' }),
  role: varchar('role', { length: 50 }).notNull(), // customer, assistant, system
  content: text('content').notNull(),
  metadata: json('metadata').$type<Record<string, any>>().default({}),
  sent_at: timestamp('sent_at').defaultNow(),
});

// ===== Prompt Testing & Analytics Tables =====

// Prompt test history with dealership_id for isolation
export const promptTests = pgTable('prompt_tests', {
  id: serial('id').primaryKey(),
  dealership_id: integer('dealership_id').notNull().references(() => dealerships.id, { onDelete: 'cascade' }),
  user_id: integer('user_id').references(() => users.id),
  original_prompt: text('original_prompt').notNull(),
  processed_prompt: text('processed_prompt'),
  ai_response: text('ai_response'),
  variables: json('variables').$type<Record<string, any>>().default({}),
  created_at: timestamp('created_at').defaultNow(),
}, table => ({
  dealershipIdx: index('test_dealership_idx').on(table.dealership_id),
}));

// User activity logs with dealership_id for isolation
export const activityLogs = pgTable('activity_logs', {
  id: serial('id').primaryKey(),
  dealership_id: integer('dealership_id').notNull().references(() => dealerships.id, { onDelete: 'cascade' }),
  user_id: integer('user_id').references(() => users.id),
  action: varchar('action', { length: 255 }).notNull(),
  resource_type: varchar('resource_type', { length: 100 }),
  resource_id: integer('resource_id'),
  details: json('details').$type<Record<string, any>>().default({}),
  ip_address: varchar('ip_address', { length: 50 }),
  user_agent: text('user_agent'),
  created_at: timestamp('created_at').defaultNow(),
}, table => ({
  dealershipIdx: index('log_dealership_idx').on(table.dealership_id),
}));

// ===== Invitation System =====

// Magic link invitations with dealership_id for isolation
export const magicLinkInvitations = pgTable('magic_link_invitations', {
  id: serial('id').primaryKey(),
  dealership_id: integer('dealership_id').references(() => dealerships.id, { onDelete: 'cascade' }),
  email: varchar('email', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).$type<UserRole>().default('user'),
  token: varchar('token', { length: 255 }).notNull().unique(),
  status: varchar('status', { length: 50 }).default('pending'), // pending, accepted, expired
  invited_by: integer('invited_by').references(() => users.id),
  expires_at: timestamp('expires_at').notNull(),
  created_at: timestamp('created_at').defaultNow(),
}, table => ({
  dealershipIdx: index('invitation_dealership_idx').on(table.dealership_id),
}));

// ===== Relations =====

export const dealershipsRelations = relations(dealerships, ({ many }) => ({
  users: many(users),
  systemPrompts: many(systemPrompts),
  variables: many(dealershipVariables),
  vehicles: many(vehicles),
  customers: many(customers),
  conversations: many(conversations),
  promptTests: many(promptTests),
  activityLogs: many(activityLogs),
  invitations: many(magicLinkInvitations),
}));

export const usersRelations = relations(users, ({ one }) => ({
  dealership: one(dealerships, {
    fields: [users.dealership_id],
    references: [dealerships.id],
  }),
}));

export const conversationsRelations = relations(conversations, ({ one, many }) => ({
  dealership: one(dealerships, {
    fields: [conversations.dealership_id],
    references: [dealerships.id],
  }),
  customer: one(customers, {
    fields: [conversations.customer_id],
    references: [customers.id],
  }),
  messages: many(messages),
}));

export const messagesRelations = relations(messages, ({ one }) => ({
  conversation: one(conversations, {
    fields: [messages.conversation_id],
    references: [conversations.id],
  }),
}));

// ===== Zod Schemas =====

// Create Insert Schemas
export const insertDealershipSchema = createInsertSchema(dealerships).omit({ id: true, created_at: true, updated_at: true });
export const insertUserSchema = createInsertSchema(users).omit({ id: true, created_at: true, updated_at: true });
export const insertSystemPromptSchema = createInsertSchema(systemPrompts).omit({ id: true, created_at: true, updated_at: true });
export const insertVehicleSchema = createInsertSchema(vehicles).omit({ id: true, created_at: true, updated_at: true });
export const insertCustomerSchema = createInsertSchema(customers).omit({ id: true, created_at: true, updated_at: true });
export const insertConversationSchema = createInsertSchema(conversations).omit({ id: true, created_at: true, updated_at: true });
export const insertMessageSchema = createInsertSchema(messages).omit({ id: true, sent_at: true });
export const insertVariableSchema = createInsertSchema(dealershipVariables).omit({ id: true, created_at: true, updated_at: true });
export const insertPromptTestSchema = createInsertSchema(promptTests).omit({ id: true, created_at: true });
export const insertActivityLogSchema = createInsertSchema(activityLogs).omit({ id: true, created_at: true });
export const insertMagicLinkInvitationSchema = createInsertSchema(magicLinkInvitations).omit({ id: true, created_at: true });

// Types
export type Dealership = typeof dealerships.$inferSelect;
export type InsertDealership = z.infer<typeof insertDealershipSchema>;

export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type SystemPrompt = typeof systemPrompts.$inferSelect;
export type InsertSystemPrompt = z.infer<typeof insertSystemPromptSchema>;

export type Vehicle = typeof vehicles.$inferSelect;
export type InsertVehicle = z.infer<typeof insertVehicleSchema>;

export type Customer = typeof customers.$inferSelect;
export type InsertCustomer = z.infer<typeof insertCustomerSchema>;

export type Conversation = typeof conversations.$inferSelect;
export type InsertConversation = z.infer<typeof insertConversationSchema>;

export type Message = typeof messages.$inferSelect;
export type InsertMessage = z.infer<typeof insertMessageSchema>;

export type DealershipVariable = typeof dealershipVariables.$inferSelect;
export type InsertDealershipVariable = z.infer<typeof insertVariableSchema>;

export type PromptTest = typeof promptTests.$inferSelect;
export type InsertPromptTest = z.infer<typeof insertPromptTestSchema>;

export type ActivityLog = typeof activityLogs.$inferSelect;
export type InsertActivityLog = z.infer<typeof insertActivityLogSchema>;

export type MagicLinkInvitation = typeof magicLinkInvitations.$inferSelect;
export type InsertMagicLinkInvitation = z.infer<typeof insertMagicLinkInvitationSchema>;