/**
 * Simple script to test the system prompt with OpenAI directly
 * Run with: node test-prompt-simple.js
 */

import 'dotenv/config';
import { OpenAI } from 'openai';

const DEFAULT_SYSTEM_PROMPT = `System Prompt for Automotive Sales Agent AI
You are an automotive sales agent named {{agentName}}, working for {{dealershipName}}, a dealership committed to exceptional customer service and top-quality vehicles. Your job is to connect with customers, understand their needs, and guide them toward purchasing a vehicle from our inventory that perfectly fits their lifestyle. You're a real person to them—never hint that you're an AI or break character. Your focus is on building trust, sparking excitement, and making the car-buying process smooth and enjoyable.

Dealership Contact
Contact Details: Visit our dealership or contact us online

Resource URLs:
- Financing: {{financingUrl}}
- Trade-In Valuation: {{tradeInUrl}}
- Appointment Scheduling: {{appointmentUrl}}
- Browse Inventory: {{inventoryUrl}}

Specific Instructions
{{specificInstructions}}

Specific Constraints
{{specificConstraints}}

Always be friendly, helpful, and knowledgeable about vehicles. When customers ask about financing, direct them to {{financingUrl}}. For trade-ins, use {{tradeInUrl}}. To schedule appointments, use {{appointmentUrl}}.`;

const TEST_MESSAGE = "Hi there, I'm looking for a new SUV for my family. Can you help me?";

// Default values for replacements
const replacements = {
  agentName: "Rylie",
  dealershipName: "Example Motors",
  financingUrl: "https://www.exampledealership.com/financing",
  tradeInUrl: "https://www.exampledealership.com/trade-in-value",
  appointmentUrl: "https://www.exampledealership.com/schedule-appointment",
  inventoryUrl: "https://www.exampledealership.com/inventory",
  specificInstructions: "Always start with a casual, personal greeting. Use contractions and everyday words.",
  specificConstraints: "No Pricing or Promises: Avoid discussing costs, financing details, or delivery guarantees."
};

// Generate the customized prompt by replacing placeholders
function generateCustomizedPrompt() {
  let prompt = DEFAULT_SYSTEM_PROMPT;
  for (const [key, value] of Object.entries(replacements)) {
    prompt = prompt.replace(new RegExp(`{{${key}}}`, 'g'), value);
  }
  return prompt;
}

async function testSystemPrompt() {
  if (!process.env.OPENAI_API_KEY) {
    console.error("Error: OPENAI_API_KEY not found in environment variables.");
    return;
  }

  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    const customizedPrompt = generateCustomizedPrompt();
    console.log("Sending test message to OpenAI API...");
    
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: customizedPrompt
        },
        {
          role: 'user',
          content: TEST_MESSAGE
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    });

    console.log("\nSystem Prompt Test Results:");
    console.log("============================");
    console.log("Customer: " + TEST_MESSAGE);
    console.log("-----");
    console.log("AI Response: " + completion.choices[0].message.content);
    console.log("============================");
    console.log("Tokens used:", completion.usage.total_tokens);
  } catch (error) {
    console.error("Error calling OpenAI API:", error.message);
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", error.response.data);
    }
  }
}

// Run the test
testSystemPrompt();