/**
 * Simple standalone script to test the updated system prompt
 */
import OpenAI from 'openai';
import { DEFAULT_SYSTEM_PROMPT } from './server/services/system-prompts/default.js';

// Retrieve API key from environment
const apiKey = process.env.OPENAI_API_KEY;

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: apiKey,
});

// Sample inventory for testing
const SAMPLE_INVENTORY = `
- 2024 Toyota RAV4 Hybrid XLE (Silver): AWD, 2.5L Hybrid Engine, 41 MPG City/38 Highway, Leather Seats, Sunroof, $34,995
- 2023 Honda Civic EX (Blue): FWD, 1.5L Turbo Engine, 33 MPG City/42 Highway, Android Auto/Apple CarPlay, $26,450
- 2024 Ford F-150 XLT (Black): 4x4, 3.5L EcoBoost, Leather Interior, Navigation, Towing Package, $48,995
- 2022 Chevrolet Equinox LT (White): FWD, 1.5L Turbo, Heated Seats, Remote Start, Teen Driver Technology, $27,500
- 2023 Tesla Model Y (Red): AWD, Dual Motor, 330 Mile Range, Premium Interior, 15" Touchscreen, Autopilot, $59,990
`;

// Sample customer questions to test
const TEST_QUESTIONS = [
  "Do you have any SUVs with good gas mileage?",
  "How much is the Toyota RAV4?",
  "Can I get financing for a Honda Civic?",
  "What electric vehicles do you have?",
  "When can I come for a test drive?"
];

// Function to process a message with the updated system prompt
async function testPrompt(customerMessage) {
  console.log('\n--------------------------------');
  console.log(`TESTING: "${customerMessage}"`);
  console.log('--------------------------------\n');
  
  // Format the system prompt with placeholder values
  const formattedPrompt = DEFAULT_SYSTEM_PROMPT
    .replace('[ARG-Agent Name]', 'Rylie')
    .replace('[ARG-Employer Name]', 'PureCars Dealership')
    .replace('[ARG-Information About Employer]', 'A family-owned dealership since 1987')
    .replace('[ARG-Products]', 'New and certified pre-owned vehicles')
    .replace('[ARG-Employer Contact Details]', 'Phone: ************, Email: <EMAIL>')
    .replace('[ARG-Name]', 'Alex')
    .replace('[ARG-Contact Details]', 'Phone: ************')
    .replace('[INPUT-Product Inventory]', SAMPLE_INVENTORY)
    .replace('[INPUT-CUSTOMER NAME]', 'Customer')
    .replace('[INPUT-CONVERSATION]', 'No previous conversation');
  
  try {
    // Call OpenAI with the updated system prompt
    const completion = await openai.chat.completions.create({
      model: 'gpt-4-turbo-preview',
      messages: [
        {
          role: 'system',
          content: formattedPrompt
        },
        {
          role: 'user',
          content: customerMessage
        }
      ],
      temperature: 0.7,
      max_tokens: 800,
      response_format: { type: 'json_object' }
    });
    
    // Extract the response content
    const responseContent = completion.choices[0].message.content;
    
    try {
      // Parse and display the JSON response
      const jsonResponse = JSON.parse(responseContent);
      
      console.log('AI RESPONSE:');
      console.log('\n' + jsonResponse.answer + '\n');
      
      // Show some analytics about the response
      console.log('ANALYTICS:');
      console.log(`- Sales Readiness: ${jsonResponse.sales_readiness}`);
      console.log(`- Type: ${jsonResponse.type}`);
      console.log(`- Quick Insights: ${jsonResponse.quick_insights}`);
      
      console.log('\n--------------------------------\n');
    } catch (error) {
      console.error('Error parsing response:', error);
      console.log('Raw response:', responseContent);
    }
  } catch (error) {
    console.error('Error calling OpenAI API:', error);
  }
}

// Run the tests
async function runTests() {
  console.log('\n=== TESTING UPDATED SYSTEM PROMPT ===\n');
  
  if (!apiKey) {
    console.error('Error: OpenAI API key not found in environment variables.');
    console.log('Please make sure OPENAI_API_KEY is set.');
    return;
  }
  
  console.log('Running tests with sample questions...\n');
  
  // Run each test question
  for (const question of TEST_QUESTIONS) {
    await testPrompt(question);
  }
  
  console.log('Tests completed! The updated system prompt is now active in your production system.');
}

// Start testing
runTests();